<script setup lang="ts">
import { onBeforeMount, ref, watch, computed } from 'vue'
import IOrder, { IOrderShippingAgent } from 'src/models/OrderModel'
import { OrderStatus, ADRClass } from 'src/models/Enums'
import OrderService from 'src/services/OrderService'
import { useRouter } from 'vue-router'
import OrderStatusService from 'src/services/OrderStatusService'
import OrderTypeService from 'src/services/OrderTypeService'
import { useAuthStore } from 'stores/auth-store'
import { QDialog, useQuasar } from 'quasar'
import RouteService from 'src/services/GoogleService'
import InputAutoCompleteForm from 'components/Route/InputAutoCompleteForm.vue'
import { Camera, CameraResultType } from '@capacitor/camera'
import { createWorker } from 'tesseract.js'
import Compressor from 'compressorjs'
import pdfMake from 'pdfmake/build/pdfmake'
import pdfFonts from 'pdfmake/build/vfs_fonts'
import { useI18n } from 'vue-i18n'
import { usePDF, VuePDF } from '@tato30/vue-pdf'
import IAddress from 'src/models/AddressModel'
import { IIdObjectModel } from 'src/models/IdObjectModel'

import AddPartnerComponent from 'src/components/PartnerManagement/AddPartnerComponent.vue'
import PartnerService from 'src/services/PartnerService'
import AIService from 'src/services/AIService'
import { mapAIDataToForm, getAdrClassTranslationKey } from 'src/utility/helperFunctions'
import IPartner from 'src/models/PartnerModel'
import PalleteTypeService from 'src/services/PalleteTypeService'
import ExtractorSettingsComponent from 'src/components/DocumentExtraction/ExtractorSettingsComponent.vue'

try {
  if (typeof pdfFonts === 'object') {
    if (pdfFonts.pdfMake && pdfFonts.pdfMake.vfs) {
      pdfMake.vfs = pdfFonts.pdfMake.vfs
    } else {
      // @ts-ignore - handle alternative format
      pdfMake.vfs = pdfFonts
    }
  }
} catch (e) {
  console.error('Error initializing pdfMake:', e)
}

const { t } = useI18n()
const $q = useQuasar()
const isLoading = ref(false)
const userStore = useAuthStore()
const companyId = userStore.companyId
const router = useRouter()
const order = ref<IOrder>()
const isCreate = ref<boolean>()
const orderId = ref<string | undefined>()
const show = ref(false)
const label = ref(null)
const dialog = ref<boolean | null>(null)
const addPartnerDialog = ref(false)
const partnerMode = ref('')
const selectedPartner = ref(null)
const partners = ref<IPartner[]>([])
const filteredPartners = ref([...partners.value])
const filteredPartnersDelivery = ref([...partners.value])

const imagesHolder = ref([])
const images = ref([])
const fileRef = ref(null)
const fileInputRef = ref(null)
const selectedFile = ref(null)
const pdfs = ref([])
const docIdsDel = ref<Array<IIdObjectModel>>([])

const orderAssignments = ref<IOrderShippingAgent[]>([])
const orderShipments = ref<any[]>([])
const isAIExtraction = ref(true)
const selectedAdrClass = ref<ADRClass>(ADRClass.NotDangerous)

const pendingFiles = ref([])
const pdfPreviewDialog = ref(false)
const currentPdf = ref(null)

const extractionSettingsVisible = ref(false);
const extractionFields = ref([]);
const customPrompt = ref('');

interface IOrderStatus {
  orderStatusId: number;
  name: string;
}

interface IOrderType {
  orderTypeId: number
  name: string
}

interface IPalleteType {
  palleteTypeId: number
  name: string
  length: number
  width: number
  abrv: string
}

//const orderStatuses = ref<OrderStatus[]>([])
const orderStatuses = ref<IOrderStatus[]>([])
const orderTypes = ref<IOrderType[]>([])
const palleteTypes = ref<IPalleteType[]>([])
// Extended formData to include new fields from the image
const formData = ref<IOrder>({
  id: '',
  externalReference: '',
  dsvIdentifier: '',
  companyId: companyId,
  trackingNumber: '',
  responsiblePerson: '',
  responsiblePersonContact: '',
  dateCreated: undefined,
  dateUpdated: undefined,
  datePickup: undefined,
  dateDelivery: undefined,
  parity: undefined,
  ransom: undefined,
  partnerNamePickup: '',
  partnerNameDelivery: '',
  contactDelivery: '',
  contactPickup: '',
  contactDeliveryPhone: '',
  contactPickupPhone: '',
  isAdr: false,
  isReturnablePackaging: false,
  returnPalleteTypeId1: 0,
  returnPalleteTypeId2: 0,
  returnPalleteType1: {
    palleteTypeId: 0,
    name: '',
    length: 0,
    width: 0,
    abrv: ''
  },
  returnPalleteType2: {
    palleteTypeId: 0,
    name: '',
    length: 0,
    width: 0,
    abrv: ''
  },
  isDeleted: false,
  length: 0,
  width: 0,
  height: 0,
  //file: null,
  partnerPickupId: '',
  partnerDeliveryId: '',
  partnerPickup: undefined,
  partnerDelivery: undefined,

  orderStatusId: undefined,
  orderStatus: {
    orderStatusId: 0,
    name: ''
  },
  orderTypeId: undefined,
  orderType: {
    orderTypeId: 0,
    name: ''
  },
  palleteTypeId: undefined,
  palleteType: {
    palleteTypeId: 0,
    name: '',
    length: 0,
    width: 0,
    abrv: ''
  },
  goodsType: '',

  sourcePickupAddress: {
    id: '',
    street: '',
    city: '',
    postalCode: '',
    country: '',
    latitude: '',
    longitude: ''
  },
  finalDestinationAddress: {
    id: '',
    street: '',
    city: '',
    postalCode: '',
    country: '',
    latitude: '',
    longitude: '',
  },
  docsIdDelete: [] as Array<number>,
  documents: [],
  notes: '',
  numberOfParcels: 0,
  weight: 0,
  volume: 0,
  lDM: 0,
  numberOfReturnedPalletes1: 0,
  numberOfReturnedPalletes2: 0,
  signedDocuments: false,
})

const adrClassOptions = computed(() => {
  return Object.values(ADRClass)
    .filter(value => typeof value === 'number') // Filter out reverse mapping keys
    .map(value => ({
      label: t(getAdrClassTranslationKey(value as ADRClass)),
      value: value as ADRClass
    }))
})

// Debug computed property to check order types
const debugOrderTypes = computed(() => {
  console.log('Order types in computed:', orderTypes.value)
  return orderTypes.value
})

const minimalOrder = {
  externalReference: 'TEST123',
  dsvIdentifier: 'DSV123',
  companyId: formData.value.companyId,
  orderTypeId: 1,
  orderStatusId: 1,
  goodsType: 'Test',
  parity: 'Test',
  notes: 'Test',
  numberOfParcels: 1,
  palleteTypeId: 1,
  weight: '1',
  ransom: '0',
  responsiblePerson: 'Test Person',
  volume: '1',
  lDM: 2.0,
  datePickup: '2025-04-30T15:25:00.000Z',
  dateDelivery: '2025-05-01T15:25:00.000Z',
  signedDocuments: false,
  partnerPickupId: 'xzbOehmRZ6m',
  partnerDeliveryId: 'fR93nMgyu16',
  contactPickupPhone: '*********',
  contactDeliveryPhone: '*********',
  partnerNamePickup: 'Test',
  partnerNameDelivery: 'Test',
  contactPickup: 'Test',
  contactDelivery: 'Test',
  sourcePickupAddress: {
    street: 'Test Street',
    city: 'Test City',
    postalCode: '12345',
    country: 'Test Country'
  },
  finalDestinationAddress: {
    street: 'Test Street',
    city: 'Test City',
    postalCode: '12345',
    country: 'Test Country'
  },
  documents: [],
  docsIdDelete: [],
  shipments: []
}

const testApi = async () => {
  try {
    const response = await OrderService.createOrder(minimalOrder, null)
    console.log('API Response:', response)
  } catch (error) {
    console.error('API Error:', error)
  }
}

const openExtractionSettings = () => {
  // Load saved extraction settings
  const savedSettings = localStorage.getItem('extractionSettings');
  if (savedSettings) {
    try {
      extractionFields.value = JSON.parse(savedSettings);
    } catch (e) {
      console.error('Error parsing saved extraction settings:', e);
      extractionFields.value = [];
    }
  }

  // Load saved custom prompt
  const savedPrompt = localStorage.getItem('extractionCustomPrompt');
  if (savedPrompt) {
    customPrompt.value = savedPrompt;
  }

  extractionSettingsVisible.value = true;
};

/* @vite-ignore */
const loadPDFs = async () => {
  if (!order.value?.documents) return

  pdfs.value = []

  for (const doc of order.value.documents) {
    try {
      if (doc && doc.Document && doc.Document.url) {
        const {
          pdf,
          pages
        } = usePDF(doc.Document.url)
        await pdf.value
        // Fix the typing issue by using type assertion
        pdfs.value.push({
          pdf,
          pages
        } as { pdf: any, pages: any })
      }
    } catch (err) {
      console.error('Error loading PDF:', err)
    }
  }
}

const openDialog = () => {
  dialog.value = true
}

// Add the previewExistingPdf function
const previewExistingPdf = (index) => {
  // Set the current PDF to the one at the specified index
  currentPdf.value = pdfs.value[index]

  // Open the preview dialog
  pdfPreviewDialog.value = true
}

const fetchOrder = async () => {
  if (!orderId.value) return

  try {
    const retrievedOrder = await OrderService.getOrder(orderId.value.toString())
    if (retrievedOrder) {
      // Deep clone to avoid reference issues
      formData.value = JSON.parse(JSON.stringify(retrievedOrder))

      // Initialize returnPackages if there's a second return package
      if (formData.value.returnPalleteTypeId2 && palleteTypes.value?.length) {
        const matchingPalleteType = palleteTypes.value.find(pt =>
          Number(pt.palleteTypeId) === Number(formData.value.returnPalleteTypeId2)
        )

        if (matchingPalleteType) {
          formData.value.returnPalleteType2 = matchingPalleteType

          returnPackages.value = [{
            palleteType: matchingPalleteType,
            numberOfParcels: formData.value.numberOfReturnedPalletes2 || 0
          }]
        }
      } else {
        // Clear returnPackages if no second return package
        returnPackages.value = []
      }


      // Process existing documents
      if (formData.value.documents && Array.isArray(formData.value.documents)) {
        // Clear existing arrays
        pdfs.value = []

        // Process each document
        for (const doc of formData.value.documents) {
          try {
            if (doc && doc.url) {
              // For PDFs, load them for preview
              const {
                pdf,
                pages
              } = usePDF(doc.url)
              await pdf.value

              // Add to pdfs array for preview
              pdfs.value.push({
                pdf,
                pages,
                id: doc.id,
                name: doc.name || 'Document',
                url: doc.url
              })
            }
          } catch (err) {
            console.error('Error loading document:', err)
          }
        }
      }
      // Fetch order assignments
      try {
        // const agents = await OrderShippingAgentService.getOrderAgents(orderId.value)
        // if (agents && Array.isArray(agents)) {
        //   orderAssignments.value = agents
        // }
      } catch (error) {
        console.error('Error fetching order assignments:', error)
      }

      // Fetch order shipments
      try {
        // const shipments = await ShipmentService.getOrderShipments(orderId.value)
        // if (shipments && Array.isArray(shipments)) {
        //   orderShipments.value = shipments
        // }
      } catch (error) {
        console.error('Error fetching order shipments:', error)
      }

      // Ensure all select objects are properly mapped
      if (formData.value.orderTypeId && orderTypes.value?.length) {
        const matchingType = orderTypes.value.find(t =>
          Number(t.orderTypeId) === Number(formData.value.orderTypeId)
        )
        if (matchingType) formData.value.orderType = matchingType
      }

      if (formData.value.orderStatusId && orderStatuses.value?.length) {
        const matchingStatus = orderStatuses.value.find(s =>
          Number(s.orderStatusId) === Number(formData.value.orderStatusId)
        )
        if (matchingStatus) formData.value.orderStatus = matchingStatus
      }

      if (formData.value.palleteTypeId && palleteTypes.value?.length) {
        const matchingPalleteType = palleteTypes.value.find(pt =>
          Number(pt.palleteTypeId) === Number(formData.value.palleteTypeId)
        )
        if (matchingPalleteType) formData.value.palleteType = matchingPalleteType
      }

      if (formData.value.returnPalleteTypeId1 && palleteTypes.value?.length) {
        const matchingPalleteType = palleteTypes.value.find(pt =>
          Number(pt.palleteTypeId) === Number(formData.value.returnPalleteTypeId1)
        )
        if (matchingPalleteType) formData.value.returnPalleteType1 = matchingPalleteType
      }

      if (formData.value.returnPalleteTypeId2 && palleteTypes.value?.length) {
        const matchingPalleteType = palleteTypes.value.find(pt =>
          Number(pt.palleteTypeId) === Number(formData.value.returnPalleteTypeId2)
        )
        if (matchingPalleteType) formData.value.returnPalleteType2 = matchingPalleteType
      }

      // Ensure required objects exist
      if (!formData.value.partnerPickup) {
        formData.value.partnerPickup = {
          partnerId: '',
          companyName: '',
          address: '',
          city: '',
          country: '',
          postalCode: '',
          isLegalEntity: false,
          oib: '',
          mbs: '',
          vatNumber: '',
          phoneNumber: '',
          email: '',
          note: '',
          eori: false,
          timocom: '',
          transEU: '',
          dateCreated: undefined,
          dateModified: undefined,
          createdById: 0,
          companyId: companyId
        }
      }

      if (!formData.value.partnerDelivery) {
        formData.value.partnerDelivery = {
          partnerId: '',
          companyName: '',
          address: '',
          city: '',
          country: '',
          postalCode: '',
          isLegalEntity: false,
          oib: '',
          mbs: '',
          vatNumber: '',
          phoneNumber: '',
          email: '',
          note: '',
          eori: false,
          timocom: '',
          transEU: '',
          dateCreated: undefined,
          dateModified: undefined,
          createdById: 0,
          companyId: companyId
        }
      }

      if (!formData.value.sourcePickupAddress) {
        formData.value.sourcePickupAddress = {
          id: '',
          street: '',
          city: '',
          postalCode: '',
          country: '',
          latitude: '',
          longitude: '',
        }
      }

      if (!formData.value.finalDestinationAddress) {
        formData.value.finalDestinationAddress = {
          id: '',
          street: '',
          city: '',
          postalCode: '',
          country: '',
          latitude: '',
          longitude: '',
        }
      }

      console.log('Loaded order data:', formData.value)
    }
  } catch (error) {
    console.error('Error fetching order:', error)
  }
}

onBeforeMount(async () => {
  isLoading.value = true

  try {
    // First, get the route parameters
    const { query } = router.currentRoute.value
    if (query.orderId) {
      isCreate.value = false
      orderId.value = typeof query.orderId === 'string' ? query.orderId : String(query.orderId)
    } else {
      isCreate.value = true
      if (!formData.value.orderStatus) {
        formData.value.orderStatus = {
          orderStatusId: 0,
          name: ''
        }
      }
      formData.value.orderStatus.orderStatusId = OrderStatus.ACTIVE
      formData.value.orderStatus.name = 'AKTIVAN'
    }

    // Then load reference data
    await Promise.all([
      await fetchStatuses(),
      await fetchTypes(),
      await fetchPartners(),
      await fetchPalleteTypes(),
      filteredPartners.value = [...partners.value],
      filteredPartnersDelivery.value = [...partners.value],
    ])

    // Finally load order data if in edit mode
    if (!isCreate.value && orderId.value) {
      await fetchOrder()
      await loadPDFs()
    }
  } catch (error) {
    console.error('Error in initialization:', error)
  } finally {
    isLoading.value = false
  }
})

const fetchStatuses = async () => {
  try {
    const { data } = await OrderStatusService.getAll()
    orderStatuses.value = data
  } catch (error) {
    console.error('Error fetching order statuses:', error)
  }
}

const fetchPalleteTypes = async () => {
  try {
    const { data } = await PalleteTypeService.getAll()
    palleteTypes.value = data
  } catch (e) {
    console.error('Error fetching order pallete types:', e)
  }
}

const fetchTypes = async () => {
  try {
    const { data } = await OrderTypeService.getAll()
    orderTypes.value = data
    // Set default order type for new orders
    if (isCreate.value && orderTypes.value.length > 0 && !formData.value.orderType?.orderTypeId) {
      formData.value.orderType = orderTypes.value[0]
    }
  } catch (error) {
    console.error('Error fetching order types:', error)
  }
}

// watch(orderTypes, (newOrderTypes) => {
//   if (isCreate.value && newOrderTypes.length > 0 && !formData.value.orderType?.orderTypeId) {
//     formData.value.orderType = newOrderTypes[0]
//   }
// })
//
// // Add this watch to update palleteType when palleteTypes list changes
// watch(palleteTypes, (newPalleteTypes) => {
//   if (!isCreate.value && formData.value.palleteTypeId && newPalleteTypes.length > 0) {
//     const matchingPalleteType = newPalleteTypes.find(pt =>
//       Number(pt.palleteTypeId) === Number(formData.value.palleteTypeId)
//     )
//     if (matchingPalleteType) {
//       formData.value.palleteType = matchingPalleteType.palleteTypeId
//     }
//   }
// })
//
// // You already have a similar one for orderTypes, but let's add one for orderStatuses
// watch(orderStatuses, (newOrderStatuses) => {
//   if (!isCreate.value && formData.value.orderStatusId && newOrderStatuses.length > 0) {
//     const matchingStatus = newOrderStatuses.find(s =>
//       Number(s.orderStatusId) === Number(formData.value.orderStatusId)
//     )
//     if (matchingStatus) formData.value.orderStatus = matchingStatus
//   }
// })

watch(() => formData.value, (newFormData) => {
  if (!isCreate.value && palleteTypes.value.length > 0) {
    // Handle palleteType - match by ID
    if (newFormData.palleteTypeId) {
      const matchedPalleteType = palleteTypes.value.find(pt =>
        Number(pt.palleteTypeId) === Number(newFormData.palleteTypeId)
      )

      if (matchedPalleteType) {
        // Since the select component has emit-value and map-options,
        // we need to set just the ID
        formData.value.palleteType = matchedPalleteType
      }
    }

    // Handle orderType
    if (newFormData.orderTypeId && orderTypes.value.length > 0) {
      const matchedOrderType = orderTypes.value.find(ot =>
        Number(ot.orderTypeId) === Number(newFormData.orderTypeId)
      )

      if (matchedOrderType) {
        formData.value.orderType = matchedOrderType
      }
    }

    // Handle orderStatus
    if (newFormData.orderStatusId && orderStatuses.value.length > 0) {
      const matchedOrderStatus = orderStatuses.value.find(os =>
        Number(os.orderStatusId) === Number(newFormData.orderStatusId)
      )

      if (matchedOrderStatus) {
        formData.value.orderStatus = matchedOrderStatus
      }
    }
  }
}, { deep: true })

watch(() => [formData.value.palleteType, formData.value.numberOfParcels], ([newPalleteType, newNumberOfParcels]) => {
  // Convert numberOfParcels to a number explicitly
  const parcelsNum = Number(newNumberOfParcels)

  console.log('Watch triggered:', {
    palleteType: newPalleteType,
    numberOfParcels: newNumberOfParcels,
    parcelsNum: parcelsNum
  })

  if (newPalleteType && !isNaN(parcelsNum)) {
    // 1. Calculate LDM when palleteType and numberOfParcels change
    if (newPalleteType.length && newPalleteType.width) {
      // LDM calculation: (length × width × numberOfParcels) / truck width (typically 2.4m)
      let length = Number(newPalleteType.length) / 100
      let width = Number(newPalleteType.width) / 100
      const truckWidth = 2.4 // standard truck width in meters
      formData.value.lDM = ((length * width * parcelsNum) / truckWidth).toFixed(2)
      formData.value.length = length.toFixed(2)
      formData.value.width = width.toFixed(2)
      console.log('Calculated LDM:', formData.value.lDM)
    }

    // 3. Copy values to returnPalleteType1 and numberOfReturnedPalletes1
    if (!formData.value.returnPalleteType1 || formData.value.returnPalleteType1 === 0) {
      formData.value.returnPalleteType1 = newPalleteType
    }
    if (!formData.value.numberOfReturnedPalletes1) {
      formData.value.numberOfReturnedPalletes1 = parcelsNum
    }
    console.log('Copied values:', {
      returnPalleteType1: formData.value.returnPalleteType1,
      numberOfReturnedPalletes1: formData.value.numberOfReturnedPalletes1
    })
  }
}, { deep: true })

// 2. Calculate volume when height, length, and width change
watch(() => [formData.value.height, formData.value.length, formData.value.width, formData.value.numberOfParcels], ([newHeight, newLength, newWidth, newNumberOfParcels]) => {
  // Convert all dimensions to numbers explicitly
  const height = Number(newHeight)
  const length = Number(newLength)
  const width = Number(newWidth)
  const numberOfParcels = Number(newNumberOfParcels)

  console.log('Dimensions watch:', {
    height,
    length,
    width,
    numberOfParcels,
    heightType: typeof newHeight,
    lengthType: typeof newLength,
    widthType: typeof newWidth,
    numberOfParcelsType: typeof newNumberOfParcels
  })

  if (!isNaN(height) && !isNaN(length) && !isNaN(width) && !isNaN(numberOfParcels) && height > 0 && length > 0 && width > 0 && numberOfParcels > 0) {
    formData.value.volume = (height * length * width * numberOfParcels).toFixed(2)
    console.log('Calculated volume:', formData.value.volume)
  }
}, { deep: true })

const getOrderTypeName = (orderType: IOrderType | undefined): string => {
  if (!orderType) return ''
  // Use a translation key pattern like 'orderType.TYPE_NAME'
  return orderType.orderTypeId + ' - ' + t(`${orderType.name}`) || orderType.orderTypeId + ' ' + orderType.name
}

// Handle ADR checkbox change
const onAdrChange = (isAdr: boolean) => {
  console.log('ADR changed to:', isAdr)
  console.log('Available order types:', orderTypes.value)

  if (!isAdr) {
    // When ADR is unchecked, set to normal goods (typically ID 1)
    const normalGoods = orderTypes.value.find(type => type.orderTypeId === 1)
    if (normalGoods) {
      formData.value.orderType = normalGoods
      formData.value.orderTypeId = normalGoods.orderTypeId
      console.log('Reset to normal goods:', normalGoods)
    }
  } else {
    // When ADR is checked, allow selection of any order type
    // Don't automatically change the selection, let user choose
    console.log('ADR enabled - user can select dangerous goods types')
  }
}

// Handle order type selection
const onOrderTypeChange = (selectedOrderType: any) => {
  console.log('Order type selected:', selectedOrderType)

  if (selectedOrderType) {
    formData.value.orderType = selectedOrderType
    formData.value.orderTypeId = selectedOrderType.orderTypeId
    console.log('Order type set to:', selectedOrderType)
  } else {
    // Handle clearing the selection
    formData.value.orderType = undefined
    formData.value.orderTypeId = undefined
    console.log('Order type cleared')
  }
}

// Fetch partners for the select inputs
const fetchPartners = async () => {
  try {
    debugger
    isLoading.value = true
    partners.value = await PartnerService.getPartnersByCompanyId(companyId)
  } catch (error) {
    console.error('Error fetching partners:', error)
    $q.notify({
      type: 'negative',
      message: t('errorFetchingPartners')
    })
  } finally {
    isLoading.value = false
  }
}

// Filter partners for search functionality
const filterPartners = (val: string, update: (callback: () => void) => void) => {

  if (val === '') {
    update(() => {
      filteredPartners.value = [...partners.value]
    })
    return
  }

  update(() => {
    const needle = val.toLowerCase()
    filteredPartners.value = partners.value.filter(
      v => v.companyName.toLowerCase().indexOf(needle) > -1
    )
  })
}

const filterPartnersDelivery = (val: string, update: (callback: () => void) => void) => {
  if (val === '') {
    update(() => {
      filteredPartnersDelivery.value = [...partners.value]
    })
    return
  }

  update(() => {
    const needle = val.toLowerCase()
    filteredPartnersDelivery.value = partners.value.filter(
      v => v.companyName.toLowerCase().indexOf(needle) > -1
    )
  })
}

// Handle partner selection
const onPickupPartnerSelected = (partner) => {
  if (partner && partner.address) {
    // Auto-fill address details if available
    formData.value.sourcePickupAddress = {
      ...formData.value.sourcePickupAddress,
      street: partner.address || '',
      city: partner.city || '',
      postalCode: partner.postalCode || '',
      country: partner.country || '',
      //contactPerson: partner.contactPerson || '',
      //phone: partner.phoneNumber || ''
    }
  }
}

const onDeliveryPartnerSelected = (partner) => {
  if (partner && partner.address) {
    // Auto-fill address details if available
    formData.value.finalDestinationAddress = {
      ...formData.value.finalDestinationAddress,
      street: partner.address || '',
      city: partner.city || '',
      postalCode: partner.postalCode || '',
      country: partner.country || '',
      //contactPerson: partner.contactPerson || '',
      //phone: partner.phoneNumber || ''
    }
  }
}

// Open dialog to add a new partner
const openAddPartnerDialog = (mode) => {
  partnerMode.value = mode
  addPartnerDialog.value = true
}

// Open dialog to edit an existing partner
const openEditPartnerDialog = (partner) => {
  if (!partner) return
  selectedPartner.value = partner
  partnerMode.value = 'edit' // Make sure you have this variable
  addPartnerDialog.value = true
}

const onPartnerUpdated = (updatedPartner: IPartner) => {
  // Update in the partners array
  const index = partners.value.findIndex(p => p.partnerId === updatedPartner.partnerId)
  if (index !== -1) {
    partners.value[index] = updatedPartner
  } else {
    // If not found, add it to the array
    partners.value.push(updatedPartner)
  }

  if (partnerMode.value === 'pickup') {
    formData.value.partnerPickup = updatedPartner
    onPickupPartnerSelected(updatedPartner)
    // Ensure filtered array is refreshed
    filteredPartners.value = [...partners.value]
  } else if (partnerMode.value === 'delivery') {
    formData.value.partnerDelivery = updatedPartner
    onDeliveryPartnerSelected(updatedPartner)
    // Ensure filtered array is refreshed
    filteredPartnersDelivery.value = [...partners.value]
  }

  addPartnerDialog.value = false
  partnerMode.value = ''
  selectedPartner.value = null

  $q.notify({
    type: 'positive',
    message: t('partnerUpdatedSuccessfully')
  })
}

// Handle new partner creation
const onPartnerCreated = (newPartner: IPartner) => {
  // Add the new partner to the partners array if it doesn't already exist
  const partnerExists = partners.value.some(p => p.partnerId === newPartner.partnerId)
  if (!partnerExists) {
    partners.value.push(newPartner)
  }

  if (partnerMode.value === 'pickup') {
    // Set the partner for pickup
    formData.value.partnerPickup = newPartner
    onPickupPartnerSelected(newPartner)

    // Update filteredPartners to include the new partner
    filteredPartners.value = [...partners.value]
  } else if (partnerMode.value === 'delivery') {
    // Set the partner for delivery
    formData.value.partnerDelivery = newPartner
    onDeliveryPartnerSelected(newPartner)

    // Update filteredPartnersDelivery to include the new partner
    filteredPartnersDelivery.value = [...partners.value]
  }

  addPartnerDialog.value = false
  partnerMode.value = ''
  selectedPartner.value = null

  $q.notify({
    type: 'positive',
    message: t('partnerAddedSuccessfully')
  })
}

const getPlaceDetails = async (event: any) => {
  const placeId = event.placeId
  const label = event.label.toLowerCase()

  const addr = await RouteService.fetchPlaceDetailsById(placeId)

  if (label === 'from') {
    formData.value.sourcePickupAddress = {
      ...addr,
      id: addr.id || '',  // Ensure id is present
    }
  }
  if (label === 'to') {
    formData.value.finalDestinationAddress = {
      ...addr,
      id: addr.id || '',  // Ensure id is present
    }
  }
}

const setAddress = (newLabel: any) => {
  // openDialog()
  show.value = !show.value
  label.value = newLabel
  console.log(show.value)
}

const IsAddressEmpty = (address: IAddress | null | undefined): IAddress | null => {
  if (!address) {
    return null
  }

  if (
    address.street === '' &&
    address.city === '' &&
    address.country === '' &&
    address.postalCode === '' &&
    address.latitude === '' &&
    address.longitude === ''
  ) {
    return null
  }

  return address
}

const submitForm = async () => {
  isLoading.value = true
  try {
    // Create a clean data object for submission
    const submittableData = { ...formData.value }
    // Extract ids from objects properly - all are treated as objects now
    submittableData.orderTypeId = submittableData.orderType?.orderTypeId
    submittableData.orderStatusId = submittableData.orderStatus?.orderStatusId
    submittableData.palleteTypeId = submittableData.palleteType?.palleteTypeId
    submittableData.returnPalleteTypeId1 = submittableData.returnPalleteType1?.palleteTypeId
    submittableData.returnPalleteTypeId2 = submittableData.returnPalleteType2?.palleteTypeId
    // Set partner IDs from the objects
    if (submittableData.partnerPickup) {
      submittableData.partnerPickupId = submittableData.partnerPickup.partnerId
      if (submittableData.partnerPickup.companyName) {
        submittableData.partnerNamePickup = submittableData.partnerPickup.companyName
      }
    }
    if (submittableData.partnerDelivery) {
      submittableData.partnerDeliveryId = submittableData.partnerDelivery.partnerId
      if (submittableData.partnerDelivery.companyName) {
        submittableData.partnerNameDelivery = submittableData.partnerDelivery.companyName
      }
    }
    // Format dates as ISO strings if they exist
    if (submittableData.datePickup) {
      submittableData.datePickup = new Date(submittableData.datePickup).toISOString()
    }
    if (submittableData.dateDelivery) {
      submittableData.dateDelivery = new Date(submittableData.dateDelivery).toISOString()
    }
    // Add required field if missing
    if (!submittableData.parity) {
      submittableData.parity = 'Standard'
    }
    // Convert numeric values properly from the form
    submittableData.numberOfReturnedPalletes1 = Number(submittableData.numberOfReturnedPalletes1) || 0
    submittableData.numberOfParcels = Number(submittableData.numberOfParcels) || 0
    submittableData.lDM = Number(submittableData.lDM) || 0
    submittableData.numberOfReturnedPalletes2 = Number(submittableData.numberOfReturnedPalletes2) || 0
    // Clean up nested objects that shouldn't be sent to the API
    delete submittableData.orderStatus
    delete submittableData.orderType
    delete submittableData.palleteType
    delete submittableData.partnerPickup
    delete submittableData.partnerDelivery
    // Remove empty ID fields
    if (submittableData.id === '') {
      delete submittableData.id
    }
    // Clean up address ID fields
    if (submittableData.sourcePickupAddress?.id === '') {
      delete submittableData.sourcePickupAddress.id
    }
    if (submittableData.finalDestinationAddress?.id === '') {
      delete submittableData.finalDestinationAddress.id
    }
    // Remove empty latitude/longitude
    if (submittableData.sourcePickupAddress?.latitude === '') {
      delete submittableData.sourcePickupAddress.latitude
      delete submittableData.sourcePickupAddress.longitude
    }
    if (submittableData.finalDestinationAddress?.latitude === '') {
      delete submittableData.finalDestinationAddress.latitude
      delete submittableData.finalDestinationAddress.longitude
    }
    // Remove trackingNumber for new orders
    if (isCreate.value) {
      delete submittableData.trackingNumber
    }

    // Ensure arrays exist
    submittableData.documents = Array.isArray(submittableData.documents) ? submittableData.documents : []
    submittableData.docsIdDelete = Array.isArray(submittableData.docsIdDelete) ? submittableData.docsIdDelete : []
    submittableData.shipments = Array.isArray(submittableData.shipments) ? submittableData.shipments : []

    // Create the properly wrapped request object
    const requestData = {
      dto: submittableData,
      file: null // Will be replaced with actual file if available
    }

    console.log('Final submission data:', JSON.stringify(requestData, null, 2))

    // Prepare file if needed
    let file = null
    // If we have pending files, process them
    if (pendingFiles.value.length > 0) {
      try {
        // If we have images, convert them to a single PDF
        const imageFiles = pendingFiles.value.filter(f => f.type === 'image')
        if (imageFiles.length > 0) {
          file = await convertToPdf()
        }

        // For PDF files, we'll handle them separately in the backend
        // We'll need to include them in the request
        const pdfFiles = pendingFiles.value.filter(f => f.type === 'pdf')

        // Create document entries for each PDF
        for (const pdfFile of pdfFiles) {
          // We'll add placeholder entries that will be replaced with real data after upload
          submittableData.documents.push({
            id: '',
            name: pdfFile.name,
            url: ''
          })
        }
      } catch (error) {
        console.error('Error processing files:', error)
      }
    }

    // Send the request
    if (isCreate.value) {
      //await OrderService.createOrder(requestData.dto, file)
      await OrderService.createOrder(submittableData, file, pendingFiles.value.filter(f => f.type === 'pdf').map(f => f.file))
      $q.notify({
        type: 'positive',
        position: 'top',
        icon: 'check_circle',
        message: t('orderSuccesfullyCreated')
      })
      goBack()
    } else {
      if (submittableData.id) {
        //await OrderService.editOrder(submittableData.id, requestData.dto, file)
        await OrderService.editOrder(submittableData.id, submittableData, file, pendingFiles.value.filter(f => f.type === 'pdf').map(f => f.file))
        $q.notify({
          type: 'positive',
          position: 'top',
          icon: 'check_circle',
          message: t('orderSubmitedSuccesfully')
        })
        goBack()
      }
    }
  } catch (error) {
    console.error('Error saving order:', error)

    if (error.response) {
      console.error('Error response:', error.response.data)
      console.error('Status code:', error.response.status)
    }

    $q.notify({
      type: 'negative',
      position: 'top',
      icon: 'error',
      message: isCreate.value ? t('orderUnuccesfullyCreated') : t('orderSubmitedUnuccesfully')
    })
  } finally {
    isLoading.value = false
  }
}

function goBack () {
  router.go(-1)
}

async function goScanner () {
  try {
    // Capture image from camera
    await captureImage()

    // If AI extraction is enabled, process the image
    if (isAIExtraction.value) {
      // Call the AI extraction function with the captured images
      await AIDataExtraction(images.value)
    }

    // Show success notification for scan
    $q.notify({
      type: 'positive',
      position: 'center',
      icon: 'check_circle',
      message: t('docScannedSuccesfully') + '!'
    })
  } catch (error) {
    console.error('Error in scanner process:', error)

    // Show error notification
    $q.notify({
      type: 'negative',
      position: 'center',
      icon: 'error',
      message: t('errorScanning')
    })
  }

}

const getFileExtension = (blob: Blob): string => {
  const mimeType = blob.type
  switch (mimeType) {
    case 'image/jpeg':
      return 'jpg'
    case 'image/png':
      return 'png'
    case 'image/gif':
      return 'gif'
    case 'image/webp':
      return 'webp'
    case 'application/pdf':
      return 'pdf'
    default:
      return 'jpg' // fallback to jpg if unknown
  }
}

async function AIDataExtraction (images: any) {
  try {
    isLoading.value = true

    // Get images to send from holder
    let imagesToSend = imagesHolder.value

    // Create FormData object for sending files
    const fileFormData = new FormData()  // Renamed to avoid confusion

    // Loop through each image and add it to the form data
    for (let i = 0; i < imagesToSend.length; i++) {
      try {
        // Convert data URL to Blob
        const response = await fetch(imagesToSend[i])
        const blob = await response.blob()

        // Add the blob as a file to the form data
        fileFormData.append('FilesToProcess', blob, `image${i}.jpg`)
      } catch (err) {
        console.error('Error processing image:', err)
      }
    }

    // Add extraction field mappings to the request if available
    if (extractionFields.value.length > 0) {
      fileFormData.append('FieldMappings', JSON.stringify(extractionFields.value))
    }

    // Add custom prompt to the request if available
    if (customPrompt.value) {
      fileFormData.append('CustomPrompt', customPrompt.value);
    }

    // Call AI service with the properly formatted form data
    let apiResponse = await AIService.processOrderImage(fileFormData)

    console.log('AI Data Extraction Result:', apiResponse)

    // Extract the actual data from the nested structure
    if (apiResponse && apiResponse.success && apiResponse.extractedData) {
      const extractedData = apiResponse.extractedData

      // Map the AI extracted data to form fields using the configured mappings
      await mapAIDataToForm(extractedData, formData, palleteTypes.value, orderTypes.value)

      $q.notify({
        type: 'positive',
        position: 'top',
        icon: 'check_circle',
        message: t('dataExtractedSuccessfully')
      })
    } else {
      throw new Error('No valid data extracted')
    }
  } catch (error) {
    console.error('Error in AI data extraction:', error)

    $q.notify({
      type: 'negative',
      position: 'top',
      icon: 'error',
      message: t('errorExtractingData')
    })
  } finally {
    isLoading.value = false
  }
}

async function captureImage () {
  images.value = []
  // const deviceInfo = await deviceService.getDeviceInfo()
  // if (deviceInfo.platform === 'web') {
  const image = await Camera.getPhoto({
    quality: 100,
    allowEditing: false,
    resultType: CameraResultType.DataUrl
  })

  formData.value.documents.forEach(doc => {
    const IIdObjectModel = { id: doc.id }
    docIdsDel.value.push(IIdObjectModel)
  })
  formData.value.docsIdDelete = docIdsDel.value.map(item => Number(item.id))
  pdfs.value.splice(0, pdfs.value.length)
  images.value.push(image.dataUrl)
  images.value.forEach(item => imagesHolder.value.push(item))
  console.log('blaaa ', images)
  console.log(imagesHolder.value)
  // trenutno nije potreban ocr iako nebi bilo lose pokusati napraviti algoritam koji bi na temelju ocra pokusao izvuci adrese.
  // await executeOCR(image.dataUrl)
  // }
}

async function removeImage (index: number) {
  $q.dialog({
    title: t('removePicture'), // 'Ukloni sliku',
    message: t('areYouSure'), // 'Jeste li sugurni ?',
    cancel: true,
    persistent: true
  }).onOk(() => {
    const doc = imagesHolder.value.splice(index, 1)
    // formData.value.idDocsDelete?.push(pic.id)
    // ePodBroj.value = ''
  })
}

async function ponoviScan () {
  // await captureImage()
  $q.dialog({
    title: t('removeDocs'), // 'Izbriši dokumente',
    message: t('areYouSureYouWantToRemoveScannedDocs'), // 'Jeste li sigurni da želite izbrisati sve skenirane dokumente ?',
    cancel: true,
    persistent: true
  }).onOk(async () => {
    images.value = []
    imagesHolder.value = []
  })
}

async function deleteUploadedFiles () {

}

async function dodajSliku () {
  await captureImage()
}

function getImageDimensions (file: string): Promise<{ w: number, h: number }> {
  return new Promise(function (resolved, rejected) {
    const i = new Image()
    i.onload = function () {
      resolved({
        w: i.width,
        h: i.height
      })
    }
    i.src = file
  })
}

const executeOCR = async (img: any) => {
  const dimensions = await getImageDimensions(img)
  const rectangles = [
    {
      left: dimensions.w / 2,
      top: 0,
      width: dimensions.w / 2,
      height: dimensions.h / 3
    }
  ]

  const worker = await createWorker('hrv')
  const ret = await worker.recognize(img, { rectangle: rectangles[0] })
  console.log('OCR Result:')
  console.log(ret.data.text)
  const result = ret.data.text.split(/\r?\n/)
  console.log(result)
  await worker.terminate()
}

async function convertToPdf () {
  const pdf = await createPdf()

  const pdfFinal = await new Promise<Blob>((resolve, reject) => {
    try {
      pdf.getBlob((blob) => {
        resolve(blob)
      }), (errorResponse) => {
        $q.loading.hide()

        reject(errorResponse)
      }
    } catch (e) {
      throw e
    }
  })
  const timestamp = new Date().getTime()
  const randomChars = Math.random().toString(36).substring(2, 8) // Adjust the substring range as needed
  const filename = `file_${timestamp}_${randomChars}.pdf`

  const file = new File([pdfFinal], filename, { type: 'application/pdf' })
  return file
}

async function compressImage (blob: Blob) {
  const final = {
    rotate: false,
    blob: null,
    canvasW: 500,
    canvasH: 500
  }
  const result = await new Promise((resolve, reject) => {
    // new Compressor(blob, { quality: 0.6,
    //   success: resolve,
    //   error: reject
    // })
    new Compressor(blob, {
      quality: 0.4,
      convertSize: 1000000, // 1mb
      convertTypes: ['image/png', 'image/webp', 'image/jpeg'],
      beforeDraw (context, canvas) {
        final.canvasH = canvas.height
        final.canvasW = canvas.width
        if (canvas.width > canvas.height) {
          final.rotate = true
        }
      },
      success (result) {
        resolve(result)
      },
      error (err) {
        console.log(err.message)
        reject(err)
      }

    })
  })
  final.blob = result.constructor.name === 'Blob' ? result : blob

  return final
}

function blobToBase64 (blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onloadend = () => {
      if (typeof reader.result === 'string') {
        resolve(reader.result)
      } else if (reader.result instanceof ArrayBuffer) {
        reject(new Error('Expected string but got ArrayBuffer'))
      } else {
        reject(new Error('Reader result is null'))
      }
    }
    reader.readAsDataURL(blob)
  })
}

const createPdf = async () => {
  try {
    const docDefinition: any = {
      pageOrientation: 'portrait',
      content: []
    }

    for (let i = 0; i < imagesHolder.value.length; i++) {
      const imageData = imagesHolder.value[i]
      if (!imageData) continue

      const p: any = {
        image: imageData,
        width: 520,
        height: 760,
        pageOrientation: 'portrait',
        pageBreak: i > 0 ? 'before' : undefined
      }

      try {
        const visual = await fetch(imageData)
        const visualAsBlob = await visual.blob()
        const compressed = await compressImage(visualAsBlob)
        if (compressed.blob) {
          const b64 = await blobToBase64(compressed.blob)
          p.image = b64
        }
      } catch (err) {
        console.error('Error processing image:', err)
        p.image = imageData // fallback to original
      }

      docDefinition.content.push(p)
    }

    return pdfMake.createPdf(docDefinition)
  } catch (error) {
    console.error('Error creating PDF:', error)
    throw error
  }
}

const notifyAndClose = () => {
  $q.notify({
    type: 'positive',
    position: 'center',
    icon: 'check_circle',
    message: t('docScannedSuccesfully') + '!'
  })
}

// Replace the file selection handler
const handleFileSelection = async (files) => {
  console.log('Files selected:', files)

  let file

  // Handle both array of files and single file object
  if (Array.isArray(files) && files.length > 0) {
    file = files[0]
  } else if (files && typeof files === 'object' && files.type) {
    // It's already a single file object
    file = files
  } else {
    console.log('No valid files provided:', files)
    return
  }

  // Now we have a single file to work with
  console.log('Processing file:', file)

  if (file.type.startsWith('image/')) {
    // If it's an image, convert to data URL for preview
    const reader = new FileReader()
    reader.onload = (e) => {
      if (e.target && e.target.result) {
        // Add to imagesHolder for preview
        imagesHolder.value.push(e.target.result)

        // Store the file for later upload
        pendingFiles.value.push({
          file: file,
          preview: e.target.result,
          type: 'image',
          name: file.name
        })

        console.log('Image prepared for preview')
        // Reset the file input
        selectedFile.value = null
      }
    }
    reader.readAsDataURL(file)
  } else if (file.type === 'application/pdf') {
    // For PDF, create a preview URL
    const previewUrl = URL.createObjectURL(file)

    // Store the file for later upload
    pendingFiles.value.push({
      file: file,
      preview: previewUrl,
      type: 'pdf',
      name: file.name
    })

    console.log('PDF prepared for preview')
    selectedFile.value = null
  } else {
    console.log('Unsupported file type:', file.type)
    $q.notify({
      type: 'warning',
      message: t('unsupportedFileType')
    })
  }
}

// Add a function to remove a pending file
const removePendingFile = (index) => {
  const fileToRemove = pendingFiles.value[index]

  // If it's a PDF, revoke the object URL to prevent memory leaks
  if (fileToRemove.type === 'pdf') {
    URL.revokeObjectURL(fileToRemove.preview)
  }

  // Remove from imagesHolder if it's an image
  if (fileToRemove.type === 'image') {
    const imageIndex = imagesHolder.value.findIndex(img => img === fileToRemove.preview)
    if (imageIndex !== -1) {
      imagesHolder.value.splice(imageIndex, 1)
    }
  }

  // Remove from pendingFiles
  pendingFiles.value.splice(index, 1)
}

const removeExistingPdf = (index) => {
  const pdfToRemove = pdfs.value[index]

  // Add the document ID to the list of documents to delete
  if (pdfToRemove && pdfToRemove.id) {
    const docIdObject = { id: pdfToRemove.id }
    docIdsDel.value.push(docIdObject)
    formData.value.docsIdDelete.push(Number(pdfToRemove.id))
  }

  // Remove from the pdfs array
  pdfs.value.splice(index, 1)

  // Also remove from formData.documents if it exists there
  if (formData.value.documents && Array.isArray(formData.value.documents)) {
    const docIndex = formData.value.documents.findIndex(doc => doc.id === pdfToRemove.id)
    if (docIndex !== -1) {
      formData.value.documents.splice(docIndex, 1)
    }
  }

  $q.notify({
    type: 'info',
    message: t('documentMarkedForDeletion')
  })
}

const createPalleteValue = (val: any, done: any) => {
  if (val.length > 2) {
    const existingType = palleteTypes.value.find(
      type => type.name.toLowerCase() === val.toLowerCase() ||
        type.abrv.toLowerCase() === val.toLowerCase()
    )

    if (!existingType) {
      // Create a new palleteType object with a temporary ID
      const newPalleteType = {
        palleteTypeId: -1, // Temporary ID that will be replaced when saved to DB
        name: val,
        abrv: val.substring(0, 3).toUpperCase() // Generate abbreviation from first 3 chars
      }

      // Add to local array for immediate use
      palleteTypes.value.push(newPalleteType)

      // Select the newly created value
      done(newPalleteType, 'add-unique')

      // Optionally, you could save this to your database
      // This depends on your API structure
      // PalleteTypeService.create(newPalleteType).then(response => {
      //   // Update the temporary ID with the real one from the server
      //   const index = palleteTypes.value.findIndex(p => p.name === val);
      //   if (index !== -1) {
      //     palleteTypes.value[index].palleteTypeId = response.palleteTypeId;
      //   }
      // });
    } else {
      done(existingType, 'add-unique')
    }
  }
}

// Add this to your refs
const returnPackages = ref([])

// Add these methods
const addReturnPackage = () => {
  returnPackages.value.push({
    palleteType: null,
    numberOfParcels: 0
  })
}

const removeReturnPackage = (index) => {
  returnPackages.value.splice(index, 1)
}

</script>

<template>
  <div>
    <q-toolbar class="bg-brand text-white">
      <q-toolbar-title>
        {{ isCreate ? $t('addOrder') : $t('editOrder') }}
      </q-toolbar-title>
      <q-btn
        flat
        round
        dense
        icon="arrow_back"
        class="q-mr-sm"
        @click="$router.go(-1)"
      />
    </q-toolbar>
    <q-separator/>

    <div class="q-pa-md" style="max-width: 1300px; margin: 0 auto;">
      <q-form @submit="submitForm">
        <q-scroll-area class="list">
          <div class="q-pa-md">
            <!-- Document scanner and preview area -->
            <div class="row justify-between q-gutter-sm">

              <div class="row q-gutter-sm col-12 col-md-6">
                <q-file
                  ref="fileInputRef"
                  v-model="selectedFile"
                  accept="image/*, application/pdf"
                  @update:model-value="handleFileSelection"
                  :label="$t('uploadFile')"
                  dense
                  outlined
                  elevated
                  standout
                  class="q-mb-md"
                  hide-hint
                  clearable
                >
                  <template v-slot:prepend>
                    <q-icon name="attach_file"/>
                  </template>
                </q-file>

                <q-btn size="md" icon="document_scanner" :label="$t('documentScanner')" outline ripple
                       @click="goScanner" color="brand"
                       class="q-mb-md float-right">
                </q-btn>
                <q-btn size="md" icon="settings" outline ripple
                       @click="openExtractionSettings" color="brand"
                       class="q-mb-md float-right">
                </q-btn>
                <!--                <q-tooltip anchor="top middle" self="bottom middle" :delay="1000">-->
                <!--                  {{ $t('enableAIExtraction') }}-->
                <!--                </q-tooltip>-->

                <q-checkbox
                  v-model="isAIExtraction"
                  :label="$t('aiExtraction')"
                  color="primary"
                  class="q-mb-md"
                />

              </div>
              <div class="col-6 col-md-3">
                <q-select
                  v-if="!isCreate.value"
                  class="q-mb-sm"
                  filled
                  v-model="formData.orderStatus"
                  :options="orderStatuses"
                  :option-value="(opt: IOrderStatus) => opt"
                  :option-label="(opt: IOrderStatus) => opt.name"
                  :label="$t('orderStatus').concat('')"
                  :disable="isCreate"
                  dense
                />
              </div>

            </div>

            <!-- Document preview section -->
            <div class="q-mb-md">
              <q-separator v-if="imagesHolder.length > 0 || pendingFiles.length > 0 || pdfs.length > 0"/>
              <div class="">
                <div class="q-col-gutter-xs row items-start">
                  <!-- Image previews -->
                  <div class="col-6 col-sm-4 col-md-3" v-for="(item, index) in imagesHolder" :key="'img-'+index">
                    <q-img
                      :src="item"
                      :fit="'scale-down'"
                    >
                      <q-icon @click="removeImage(index)" class="absolute all-pointer-events" size="32px" name="cancel"
                              color="black" style="top: 8px; left: 8px">
                      </q-icon>
                    </q-img>
                  </div>

                  <!-- Pending PDF previews -->
                  <div class="col-6 col-sm-4 col-md-3"
                       v-for="(item, index) in pendingFiles.filter(f => f.type === 'pdf')" :key="'pdf-'+index">
                    <q-card class="pdf-preview">
                      <q-card-section>
                        <div class="row items-center">
                          <q-icon name="picture_as_pdf" size="2rem" color="negative" class="q-mr-sm"/>
                          <div class="text-subtitle2 ellipsis">{{ item.name }}</div>
                        </div>
                      </q-card-section>
                      <q-card-actions class="justify-between">
                        <q-btn flat round dense icon="cancel"
                               @click="removePendingFile(pendingFiles.findIndex(f => f === item))"/>
                      </q-card-actions>
                    </q-card>
                  </div>

                  <!-- Existing PDFs -->
                  <div class="col-6 col-sm-4 col-md-3" v-for="(item, index) in pdfs" :key="'existing-pdf-'+index">
                    <q-card class="pdf-preview">
                      <q-card-section>
                        <div class="row items-center">
                          <q-icon name="picture_as_pdf" size="2rem" color="negative" class="q-mr-sm"/>
                          <div class="text-subtitle2 ellipsis">{{ item.name || `Document ${index + 1}` }}</div>
                        </div>
                      </q-card-section>
                      <q-card-actions class="justify-between">
                        <q-btn flat round dense icon="visibility" @click="previewExistingPdf(index)"/>
                        <q-btn flat round dense icon="cancel" @click="removeExistingPdf(index)"/>
                      </q-card-actions>
                    </q-card>
                  </div>
                </div>
              </div>
              <q-separator v-if="imagesHolder.length > 0 || pendingFiles.length > 0 || pdfs.length > 0"/>
            </div>

            <!-- Add this section for PDF preview after the images preview section -->

            <div class="text-h6 q-mb-md">{{ $t('orderDetails') }}</div>
            <!-- Main order data section -->
            <div class="row q-col-gutter-md">

              <!-- Left column - Order details -->
              <div class="col-12 col-md-6">


                <!-- DSVId (renamed from name) -->
                <!-- DSVId (renamed from name) -->
                <div class="row q-col-gutter-sm">
                  <div class="col-12 col-sm-6">
                    <q-input class="q-mb-sm" type="text" v-model="formData.dsvIdentifier"
                             :label="$t('dsvIdentifier').concat('*')" dense
                             outlined required></q-input>
                  </div>
                  <div class="col-12 col-sm-6">
                    <q-input class="q-mb-sm" type="text" v-model="formData.externalReference" :label="$t('reference')"
                             dense
                             outlined></q-input>
                  </div>
                  <div class="col-12 col-sm-6">
                    <q-input class="q-mb-sm" type="text" v-model="formData.responsiblePerson"
                             :label="($t('OdgovornaOsoba')).concat('')"
                             dense outlined></q-input>
                  </div>
                  <div class="col-12 col-sm-6">
                    <!-- Order Status - only show in edit mode -->
                    <q-input class="q-mb-sm" type="text" v-model="formData.responsiblePersonContact"
                             :label="($t('Kontakt')).concat('')"
                             dense outlined></q-input>

                  </div>
                  <div class="col-12 col-sm-6">
                    <q-input type="text" v-model="formData.goodsType" :label="$t('goodsType')" dense outlined></q-input>
                  </div>
                  <div class="col-12 col-sm-6 q-mb-sm">
                    <q-input type="string" v-model="formData.weight" :label="$t('weight') + ' (kg)'" dense
                             outlined></q-input>
                  </div>
                </div>

                <div class="col-12">
                  <q-input type="textarea" v-model="formData.notes"
                           :label="$t('notes')" dense outlined rows="2" autogrow></q-input>
                </div>


                <! -- ----------------treba vidjeti kada se ovo aktivira i kako izgleda onda
                forma---------------------->
                <!-- Order assignments section -->
                <!--                <q-separator v-if="orderAssignments.length > 0 || true" class="q-my-md"/>-->
                <!--                <div v-if="orderAssignments.length > 0 || true">-->
                <!--                  <div class="text-subtitle2 q-mb-sm">{{ $t('orderAssignedTo') }}:</div>-->
                <!--                  <div class="row q-gutter-sm">-->
                <!--                    <q-chip-->
                <!--                      v-for="(assignment, index) in orderAssignments"-->
                <!--                      :key="index"-->
                <!--                      icon="assignment_ind"-->
                <!--                      :color="assignment.isExecutor ? 'primary' : 'grey-7'"-->
                <!--                      text-color="white"-->
                <!--                    >-->
                <!--                      <q-avatar v-if="assignment.isExecutor">-->
                <!--                        <q-icon name="verified"/>-->
                <!--                      </q-avatar>-->
                <!--                      {{ assignment.shippingAgent?.companyName || assignment.shippingAgent?.username }}-->
                <!--                      <q-badge v-if="assignment.isExecutor" color="green">{{ $t('executor') }}</q-badge>-->
                <!--                    </q-chip>-->
                <!--                  </div>-->

                <!--                  <div v-if="orderShipments.length > 0" class="text-subtitle2 q-mt-md q-mb-sm">{{-->
                <!--                      $t('shipmentCarriers')-->
                <!--                    }}:-->
                <!--                  </div>-->
                <!--                  <div class="row q-gutter-sm">-->
                <!--                    <q-chip-->
                <!--                      v-for="(shipment, index) in orderShipments"-->
                <!--                      :key="'s'+index"-->
                <!--                      icon="local_shipping"-->
                <!--                      color="teal"-->
                <!--                      text-color="white"-->
                <!--                    >-->
                <!--                      {{ shipment.carrierName || shipment.carrier?.companyName }}-->
                <!--                      <q-badge color="orange" v-if="shipment.shipmentStatus">-->
                <!--                        {{ shipment.shipmentStatus.name }}-->
                <!--                      </q-badge>-->
                <!--                    </q-chip>-->
                <!--                  </div>-->
                <!--                </div>-->
                <! -- -------------------------------------->


              </div>

              <!-- Right column - Shipment details -->
              <div class="col-12 col-md-6">
                <!--                <div class="text-h6 q-mb-md">{{ $t('shipmentDetails') }}</div>-->

                <div class="row q-col-gutter-sm">
                  <div class="col-12 col-sm-6">
                    <q-select
                      v-model="formData.palleteType"
                      :options="palleteTypes"
                      :option-label="(opt) => opt && typeof opt === 'object' ? `${opt.abrv || ''} ${opt.name || ''}` : ''"
                      :option-value="(opt) => opt"
                      :label="$t('palleteType')"
                      :rules="[val => !!val || $t('obligatoryField')]"
                      dense
                      filled
                      outlined
                      use-input
                      input-debounce="0"
                      @new-value="createPalleteValue"
                    >
                      <template v-slot:no-option>
                        <q-item>
                          <q-item-section class="text-grey">
                            {{ $t('notSelected') }}
                          </q-item-section>
                        </q-item>
                      </template>
                    </q-select>
                  </div>
                  <div class="col-12 col-sm-6">
                    <q-input type="number" v-model="formData.numberOfParcels" :label="$t('parcel')" dense
                             outlined></q-input>
                  </div>
                  <div class="col-12 col-sm-4">
                    <q-input type="number" v-model="formData.height" :label="$t('visina') + ' (m)'" dense
                             outlined></q-input>
                  </div>
                  <div class="col-12 col-sm-4">
                    <q-input type="number" v-model="formData.length" :label="$t('duzina') + ' (m)'" dense
                             outlined></q-input>
                  </div>
                  <div class="col-12 col-sm-4">
                    <q-input type="number" v-model="formData.width" :label="$t('sirina') + ' (m)'" dense
                             outlined></q-input>
                  </div>
                  <div class="col-12 col-sm-6">
                    <q-input type="number" v-model="formData.volume" :label="$t('volume') + ' (m³)'" dense
                             outlined></q-input>
                  </div>
                  <div class="col-12 col-sm-6">

                    <q-input type="number" v-model="formData.lDM" :label="$t('LDM')" dense outlined></q-input>

                  </div>
                  <!--                  <div class="col-12 col-sm-6">-->
                  <!--                    <q-input type="number" v-model="formData.numberOfEURPalletes" :label="$t('numberOfEURPalletes')"-->
                  <!--                             dense outlined></q-input>-->
                  <!--                  </div>-->
                  <!--                  <div class="col-12 col-sm-6">-->
                  <!--                    <q-input type="number" v-model="formData.numberOfXPalletes" :label="$t('numberOfXPalletes')" dense-->
                  <!--                             outlined></q-input>-->
                  <!--                  </div>-->
                  <div class="col-12 col-sm-6">
                    <q-checkbox v-model="formData.isAdr" color="warning" :label="$t('adrClass')"
                                outlined
                                @update:model-value="onAdrChange"></q-checkbox>
                  </div>
                  <div class="col-12 col-sm-6">

                    <q-select
                      :disable="!formData.isAdr"
                      class="q-mb-sm"
                      filled
                      v-model="formData.orderType"
                      :options="debugOrderTypes"
                      option-value="orderTypeId"
                      :option-label="getOrderTypeName"
                      :label="$t('orderType').concat(' *') + ' - Debug: ' + debugOrderTypes.length"
                      dense
                      clearable
                      @update:model-value="onOrderTypeChange"
                    >
                      <template v-slot:no-option>
                        <q-item>
                          <q-item-section class="text-grey">
                            {{ $t('noOptions') }} - Available: {{ debugOrderTypes.length }}
                          </q-item-section>
                        </q-item>
                      </template>
                    </q-select>

                  </div>
                  <div class="col-12 col-sm-4">
                    <div class="text-caption ">{{ $t('signedDocuments') }}</div>
                    <q-option-group
                      v-model="formData.signedDocuments"
                      :options="[{label: $t('da'), value: true},{label: $t('ne'), value: false}]"
                      type="radio"
                      color="primary"
                      dense
                      size="lg"
                      inline
                      emit-value
                      map-options
                    />
                  </div>
                  <div class="col-12 col-sm-4">
                    <q-input type="text" v-model="formData.ransom" :label="$t('otkupnina')" dense outlined></q-input>
                  </div>
                  <div class="col-12 col-sm-4">
                    <q-checkbox v-model="formData.isReturnablePackaging" color="warning"
                                :label="$t('isPovratnaAmbalaza')"
                                outlined></q-checkbox>
                  </div>
                  <div :style="formData.isReturnablePackaging ? '' : 'display: none;'"
                       class="col-12 q-col-gutter-sm separation-border">

                    <div class="q-col-gutter-sm">
                      <div class="text-subtitle2">{{ $t('povratna ambalaza') }}</div>

                      <!-- First row (original) -->
                      <div class="row q-col-gutter-sm">
                        <div class="col-12 col-sm-7">
                          <q-select
                            v-model="formData.returnPalleteType1"
                            :options="palleteTypes"
                            :option-label="(opt) => opt && typeof opt === 'object' ? `${opt.abrv || ''} ${opt.name || ''}` : ''"
                            :option-value="(opt) => opt"
                            :label="$t('palleteType')"
                            dense
                            filled
                            outlined
                            use-input
                            input-debounce="0"
                            @new-value="createPalleteValue"
                          >
                            <template v-slot:no-option>
                              <q-item>
                                <q-item-section class="text-grey">
                                  {{ $t('notSelected') }}
                                </q-item-section>
                              </q-item>
                            </template>
                          </q-select>
                        </div>
                        <div class="col-12 col-sm-3">
                          <q-input type="number" v-model="formData.numberOfReturnedPalletes1" :label="$t('parcel')"
                                   dense
                                   outlined></q-input>
                        </div>
                        <div class="col-12 col-sm-2">
                          <q-btn icon="add" color="green" size="small" dense @click="addReturnPackage"/>
                        </div>
                      </div>

                      <!-- Additional rows (added dynamically) -->
                      <div v-for="(pkg, index) in returnPackages" :key="index" class="row q-col-gutter-sm q-mt-sm">
                        <div class="col-12 col-sm-7">
                          <q-select
                            v-model="formData.returnPalleteType2"
                            :options="palleteTypes"
                            :option-label="(opt) => opt && typeof opt === 'object' ? `${opt.abrv || ''} ${opt.name || ''}` : ''"
                            :option-value="(opt) => opt"
                            :label="$t('palleteType')"
                            dense
                            filled
                            outlined
                            use-input
                            input-debounce="0"
                            @new-value="createPalleteValue"
                          >
                            <template v-slot:no-option>
                              <q-item>
                                <q-item-section class="text-grey">
                                  {{ $t('notSelected') }}
                                </q-item-section>
                              </q-item>
                            </template>
                          </q-select>
                        </div>
                        <div class="col-12 col-sm-3">
                          <q-input type="number" v-model="formData.numberOfReturnedPalletes2" :label="$t('parcel')"
                                   dense
                                   outlined></q-input>
                        </div>
                        <div class="col-12 col-sm-2">
                          <q-btn icon="remove" color="red" size="small" dense @click="removeReturnPackage(index)"/>
                        </div>
                      </div>
                    </div>

                  </div>

                </div>
              </div>
            </div>

            <q-separator class="q-my-md"/>

            <!-- Pickup Address Section -->
            <div class="row q-col-gutter-md">
              <div class="col-12 col-md-6">
                <div class="separation-border">
                  <div class="text-h6 q-mb-md">{{ $t('pickupDetails') }}</div>

                  <!-- Pickup Partner Selection -->
                  <div class="row q-mb-sm q-gutter-sm items-center">
                    <q-select
                      class="col"
                      v-model="formData.partnerPickup"
                      :options="filteredPartners"
                      option-value="partnerId"
                      option-label="companyName"
                      :label="$t('partner')"
                      dense
                      outlined
                      use-input
                      fill-input
                      behavior="menu"
                      hide-selected
                      input-debounce="300"
                      @filter="filterPartners"
                      @update:model-value="onPickupPartnerSelected"
                    >
                      <template v-slot:no-option>
                        <q-item>
                          <q-item-section class="text-grey">
                            {{ $t('noResults') }}
                          </q-item-section>
                        </q-item>
                      </template>
                    </q-select>
                    <q-btn icon="add" color="primary" dense flat @click="openAddPartnerDialog('pickup')"/>
                    <q-btn
                      icon="edit"
                      color="primary"
                      dense
                      flat
                      :disable="!formData.partnerPickup"
                      @click="openEditPartnerDialog(formData.partnerPickup)"
                    />
                  </div>
                  <q-input
                    v-if="formData.partnerNamePickup"
                    class="q-mb-sm"
                    type="text"
                    v-model="formData.partnerNamePickup"
                    :label="$t('extractedPartnerName')"
                    dense
                    outlined
                    readonly
                  >
                    <template v-slot:append>
                      <q-icon name="info" color="primary">
                        <q-tooltip>
                          {{ $t('extractedPartnerNameInfo') }}
                        </q-tooltip>
                      </q-icon>
                    </template>
                  </q-input>

                  <div class="col-12 col-sm-6">
                    <q-input
                      class="q-mb-sm"
                      v-model="formData.datePickup"
                      :label="$t('date')"
                      dense
                      outlined>
                      <template v-slot:prepend>
                        <q-icon name="event" class="cursor-pointer">
                          <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                            <q-date v-model="formData.datePickup" mask="YYYY-MM-DD HH:mm">
                              <div class="row items-center justify-end">
                                <q-btn v-close-popup label="Close" color="primary" flat/>
                              </div>
                            </q-date>
                          </q-popup-proxy>
                        </q-icon>
                      </template>
                      <template v-slot:append>
                        <q-icon name="access_time" class="cursor-pointer">
                          <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                            <q-time v-model="formData.datePickup" mask="YYYY-MM-DD HH:mm" format24h>
                              <div class="row items-center justify-end">
                                <q-btn v-close-popup label="Close" color="primary" flat/>
                              </div>
                            </q-time>
                          </q-popup-proxy>
                        </q-icon>
                      </template>
                    </q-input>
                  </div>

                  <InputAutoCompleteForm class="q-mb-sm"
                                         label="From"
                                         @place-id="getPlaceDetails($event)">
                  </InputAutoCompleteForm>

                  <div class="row q-col-gutter-sm">
                    <div class="col-12">
                      <q-input type="text" v-model="formData.sourcePickupAddress.street" :label="$t('street')" dense
                               outlined></q-input>
                    </div>
                    <div class="col-12 col-sm-6">
                      <q-input type="text" v-model="formData.sourcePickupAddress.city" :label="$t('city')" dense
                               outlined></q-input>
                    </div>
                    <div class="col-12 col-sm-6">
                      <q-input type="text" v-model="formData.sourcePickupAddress.postalCode" :label="$t('zipCode')"
                               dense outlined></q-input>
                    </div>
                    <div class="col-12">
                      <q-input type="text" v-model="formData.sourcePickupAddress.country" :label="$t('country')" dense
                               outlined></q-input>
                    </div>
                    <div class="col-12 col-sm-6">
                      <q-input type="text" v-model="formData.contactPickup"
                               :label="$t('contactPerson')" dense outlined></q-input>
                    </div>
                    <div class="col-12 col-sm-6">
                      <q-input type="text" v-model="formData.contactPickupPhone" :label="$t('phone')" dense
                               outlined></q-input>
                    </div>
                    <div class="col-12 col-sm-6">
                      <q-input type="text" v-model="formData.sourcePickupAddress.latitude" :label="$t('latitude')" dense
                               outlined readonly></q-input>
                    </div>
                    <div class="col-12 col-sm-6">
                      <q-input type="text" v-model="formData.sourcePickupAddress.longitude" :label="$t('longitude')"
                               dense outlined readonly></q-input>
                    </div>
                  </div>
                </div>
              </div>

              <!--              <div class="col-md-2 gt-sm"></div>-->

              <!-- Delivery Address Section -->
              <div class="col-12 col-md-6">
                <div class="separation-border">

                  <div class="text-h6 q-mb-md">{{ $t('deliveryDetails') }}</div>

                  <!-- Delivery Partner Selection -->
                  <div class="row q-mb-sm q-gutter-sm items-center">
                    <q-select
                      class="col"
                      v-model="formData.partnerDelivery"
                      :options="filteredPartnersDelivery"
                      option-value="partnerId"
                      option-label="companyName"
                      :label="$t('partner')"
                      dense
                      outlined
                      use-input
                      fill-input
                      hide-selected
                      input-debounce="300"
                      @filter="filterPartnersDelivery"
                      @update:model-value="onDeliveryPartnerSelected"
                    >
                      <template v-slot:no-option>
                        <q-item>
                          <q-item-section class="text-grey">
                            {{ $t('noResults') }}
                          </q-item-section>
                        </q-item>
                      </template>
                    </q-select>
                    <q-btn icon="add" color="primary" dense flat @click="openAddPartnerDialog('delivery')"/>
                    <q-btn
                      icon="edit"
                      color="primary"
                      dense
                      flat
                      :disable="!formData.partnerDelivery"
                      @click="openEditPartnerDialog(formData.partnerDelivery)"
                    />
                  </div>
                  <q-input
                    v-if="formData.partnerNameDelivery"
                    class="q-mb-sm"
                    type="text"
                    v-model="formData.partnerNameDelivery"
                    :label="$t('extractedPartnerName')"
                    dense
                    outlined
                    readonly
                  >
                    <template v-slot:append>
                      <q-icon name="info" color="primary">
                        <q-tooltip>
                          {{ $t('extractedPartnerNameInfo') }}
                        </q-tooltip>
                      </q-icon>
                    </template>
                  </q-input>
                  <div class="col-12 col-sm-6">
                    <q-input
                      class="q-mb-sm"
                      v-model="formData.dateDelivery"
                      :label="$t('date')"
                      dense
                      outlined>
                      <template v-slot:prepend>
                        <q-icon name="event" class="cursor-pointer">
                          <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                            <q-date v-model="formData.dateDelivery" mask="YYYY-MM-DD HH:mm">
                              <div class="row items-center justify-end">
                                <q-btn v-close-popup label="Close" color="primary" flat/>
                              </div>
                            </q-date>
                          </q-popup-proxy>
                        </q-icon>
                      </template>
                      <template v-slot:append>
                        <q-icon name="access_time" class="cursor-pointer">
                          <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                            <q-time v-model="formData.dateDelivery" mask="YYYY-MM-DD HH:mm" format24h>
                              <div class="row items-center justify-end">
                                <q-btn v-close-popup label="Close" color="primary" flat/>
                              </div>
                            </q-time>
                          </q-popup-proxy>
                        </q-icon>
                      </template>
                    </q-input>
                  </div>

                  <InputAutoCompleteForm
                    class="q-mb-sm"
                    label="To"
                    @place-id="getPlaceDetails($event)">
                  </InputAutoCompleteForm>

                  <div class="row q-col-gutter-sm">
                    <div class="col-12">
                      <q-input type="text" v-model="formData.finalDestinationAddress.street" :label="$t('street')" dense
                               outlined></q-input>
                    </div>
                    <div class="col-12 col-sm-6">
                      <q-input type="text" v-model="formData.finalDestinationAddress.city" :label="$t('city')" dense
                               outlined></q-input>
                    </div>
                    <div class="col-12 col-sm-6">
                      <q-input type="text" v-model="formData.finalDestinationAddress.postalCode" :label="$t('zipCode')"
                               dense outlined></q-input>
                    </div>
                    <div class="col-12">
                      <q-input type="text" v-model="formData.finalDestinationAddress.country" :label="$t('country')"
                               dense
                               outlined></q-input>
                    </div>
                    <div class="col-12 col-sm-6">
                      <q-input type="text" v-model="formData.contactDelivery"
                               :label="$t('contactPerson')" dense outlined></q-input>
                    </div>
                    <div class="col-12 col-sm-6">
                      <q-input type="text" v-model="formData.contactDeliveryPhone" :label="$t('phone')" dense
                               outlined></q-input>
                    </div>
                    <div class="col-12 col-sm-6">
                      <q-input type="text" v-model="formData.finalDestinationAddress.latitude" :label="$t('latitude')"
                               dense outlined readonly></q-input>
                    </div>
                    <div class="col-12 col-sm-6">
                      <q-input type="text" v-model="formData.finalDestinationAddress.longitude" :label="$t('longitude')"
                               dense outlined readonly></q-input>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!--            <q-btn color="red" size="md" label="test api" @click="testApi"/>-->
          </div>
          <q-spinner v-if="isLoading" size="50px" color="primary" class="absolute-center"/>
        </q-scroll-area>

        <!-- Action buttons -->
        <div class="q-pa-md row justify-between">
          <div>
            <q-btn style="padding: 10px" v-show="imagesHolder.length > 0" color="red" icon="delete" label="" size="md"
                   @click="ponoviScan" class="q-mr-md"/>
            <q-btn size="md" color="brand" icon="add_a_photo" @click="dodajSliku"/>
          </div>
          <q-btn type="submit" icon="save" size="md" :label="$t('submit')" color="brand"/>
        </div>
      </q-form>
    </div>

    <!-- Partner Dialog -->
    <q-dialog v-model="addPartnerDialog" persistent>
      <q-card style="width: 800px; max-width: 90vw;">
        <q-card-section class="row items-center">
          <!--          <div class="text-h6">{{ partnerMode === 'edit' ? t('editPartner') : t('addPartner') }}</div>-->
          <!--          <q-space/>-->
          <q-btn icon="close" class="absolute-right" flat round dense v-close-popup/>
        </q-card-section>
        <q-card-section>
          <AddPartnerComponent
            @partner-created="onPartnerCreated"
            @partner-updated="onPartnerUpdated"
            @cancel="addPartnerDialog = false"
            :partner="selectedPartner"
            :mode="partnerMode"
          />
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- Add the ExtractorSettingsComponent -->
    <ExtractorSettingsComponent
      v-model:visible="extractionSettingsVisible"
      v-model:extractionFields="extractionFields"
      v-model:custom-prompt="customPrompt"
      :formData="formData"
    />

    <!-- PDF Preview Dialog -->
    <q-dialog v-model="pdfPreviewDialog">
      <q-card style="width: 90vw; max-width: 90vw; height: 90vh;">
        <q-card-section class="row items-center">
          <q-space/>
          <q-btn icon="close" flat round dense v-close-popup/>
          <div class="text-h6">{{ currentPdf?.name || 'Document Preview' }}</div>
          <q-space/>
          <!--          <q-btn icon="close" flat round dense v-close-popup />-->
        </q-card-section>
        <q-card-section class="q-pa-none" style="height: calc(90vh - 50px); overflow: auto;">
          <div v-if="currentPdf">
            <div v-for="page in currentPdf.pages" :key="page">
              <VuePDF :pdf="currentPdf.pdf" :page="page" fit-parent/>
            </div>
          </div>
        </q-card-section>
      </q-card>
    </q-dialog>
  </div>
</template>

<style scoped lang="scss">
.list {
  position: relative;
  margin: 0 auto;
  height: calc(100vh - 250px);
  max-width: 100%;
}

.separation-border {
  margin-top: 16px;
  border: 1px dashed #000000;
  border-radius: 5px;
  padding: 10px;
}

.inline-file-uploader {
  display: inline-block;

  ::v-deep(.q-field__native) {
    display: none;
  }
}

.pdf-preview {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

/* Responsive adjustments */
@media (max-width: 599px) {
  .list {
    height: calc(100vh - 200px);
  }
}

</style>


