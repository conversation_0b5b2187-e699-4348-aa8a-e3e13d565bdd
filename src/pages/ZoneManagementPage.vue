<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import { useQuasar } from 'quasar'
import { useZoneStore } from 'src/stores/zone-store'
import { useRouter } from 'vue-router'
import { loader } from 'src/boot/google'
import MiniMapPreview from 'src/components/Map/MiniMapPreview.vue'
import { formatAddress } from 'src/models/FormatModels'

const $q = useQuasar()
const zoneStore = useZoneStore()
const router = useRouter()

// UI state
const activeTab = ref('map') // Set 'map' as default tab to show the map first
const isLoadingData = ref(false)
const miniMapInstances = ref<{[key: string]: google.maps.Map}>({})

// Map view state
const allZonesMap = ref(null)
const allZonesPolygons = ref<{[key: string]: google.maps.Polygon}>({})
const activeInfoWindow = ref<google.maps.InfoWindow | null>(null)

// Dialogs
const shipmentDialog = ref({
  show: false,
  zone: null
})

const driverDialog = ref({
  show: false,
  zone: null
})

const deleteDialog = ref({
  show: false,
  zone: null
})

// Computed properties
const zones = computed(() => zoneStore.getZones)
const shipments = computed(() => zoneStore.getShipments)
const drivers = computed(() => zoneStore.getDrivers)
const unassignedShipments = computed(() => zoneStore.getUnassignedShipments)
const unassignedDrivers = computed(() => zoneStore.getUnassignedDrivers)
const loading = computed(() => zoneStore.loading)

// Methods for zones
function getShipmentsForZone(zoneId) {
  return zoneStore.getShipmentsForZone(zoneId)
}

function getDriversForZone(zoneId) {
  return zoneStore.getDriversForZone(zoneId)
}

// Initialize the all zones map
const initAllZonesMap = async () => {
  try {
    const { Map } = await loader.importLibrary('maps')

    const mapElement = document.getElementById('all-zones-map')
    if (!mapElement) return

    const mapOptions = {
      center: { lat: 45.8150, lng: 15.9819 }, // Default center
      zoom: 12,
      mapTypeControl: true,
      fullscreenControl: true,
      streetViewControl: false
    }

    allZonesMap.value = new Map(mapElement, mapOptions)

    // Add all zones to map
    displayAllZonesOnMap()
  } catch (error) {
    console.error('Error initializing all zones map:', error)
  }
}

// Display all zones on the map
const displayAllZonesOnMap = () => {
  if (!allZonesMap.value) return

  // Clear existing polygons
  Object.values(allZonesPolygons.value).forEach(polygon => {
    polygon.setMap(null)
  })
  allZonesPolygons.value = {}

  // If we have no zones, center on default location
  if (zones.value.length === 0) return

  const bounds = new google.maps.LatLngBounds()

  // Add each zone to the map
  zones.value.forEach(zone => {
    try {
      // Parse path from JSON string if needed
      let path = typeof zone.coordinates === 'string'
        ? JSON.parse(zone.coordinates)
        : zone.coordinates

      // Validate coordinates
      if (!Array.isArray(path) || path.length === 0) {
        console.warn('Invalid coordinates for zone:', zone.id, path)
        return
      }

      // Transform coordinates from backend format {latitude, longitude} to Google Maps format {lat, lng}
      path = path.map(coord => ({
        lat: coord.latitude || coord.lat,
        lng: coord.longitude || coord.lng
      }))

      // Ensure coordinates have valid lat/lng values
      path = path.filter(coord => 
        coord && 
        typeof coord.lat === 'number' && 
        typeof coord.lng === 'number' && 
        !isNaN(coord.lat) && 
        !isNaN(coord.lng) &&
        isFinite(coord.lat) && 
        isFinite(coord.lng)
      )

      if (path.length === 0) {
        console.warn('No valid coordinates for zone:', zone.id)
        return
      }

      const polygon = new google.maps.Polygon({
        paths: path,
        strokeColor: zone.color || '#4285F4',
        strokeOpacity: 0.8,
        strokeWeight: 2,
        fillColor: zone.color || '#4285F4',
        fillOpacity: 0.35,
        map: allZonesMap.value
      })

      // Store reference to polygon
      allZonesPolygons.value[zone.id] = polygon

      // Add click listener to polygon
      google.maps.event.addListener(polygon, 'click', () => {
        showZoneInfoOnMap(zone, polygon)
      })

      // Add all points to bounds
      path.forEach(point => {
        bounds.extend(new google.maps.LatLng(point.lat, point.lng))
      })
    } catch (error) {
      console.error('Error displaying zone on map:', zone.id, error)
    }
  })

  // Fit map to show all zones
  if (!bounds.isEmpty()) {
    allZonesMap.value.fitBounds(bounds)
  }
}

// Show zone info window on the map
const showZoneInfoOnMap = (zone, polygon) => {
  if (!allZonesMap.value) return

  // Close any existing info window
  if (activeInfoWindow.value) {
    activeInfoWindow.value.close()
  }

  // Get the center of the polygon
  const bounds = new google.maps.LatLngBounds()
  const path = polygon.getPath()
  for (let i = 0; i < path.getLength(); i++) {
    bounds.extend(path.getAt(i))
  }

  // Create content for info window
  const contentString = `
    <div class="zone-info-window">
      <h3>${zone.name}</h3>
      <p>${zone.description || 'No description'}</p>
      <p><strong>Shipments:</strong> ${getShipmentsForZone(zone.id).length}</p>
      <p><strong>Drivers:</strong> ${getDriversForZone(zone.id).length}</p>
      <div class="zone-actions">
        <button id="map-edit-zone-${zone.id}" class="q-btn q-btn-item non-selectable q-btn--flat q-btn--rectangle q-btn--actionable q-focusable q-hoverable">Edit</button>
        <button id="map-view-zone-${zone.id}" class="q-btn q-btn-item non-selectable q-btn--flat q-btn--rectangle q-btn--actionable q-focusable q-hoverable">View Details</button>
      </div>
    </div>
  `

  // Create info window
  const infoWindow = new google.maps.InfoWindow({
    content: contentString,
    position: bounds.getCenter()
  })

  // Store active info window
  activeInfoWindow.value = infoWindow

  // Open info window
  infoWindow.open(allZonesMap.value)

  // Add event listeners after info window is loaded
  google.maps.event.addListener(infoWindow, 'domready', () => {
    // Edit button
    const editButton = document.getElementById(`map-edit-zone-${zone.id}`)
    if (editButton) {
      editButton.addEventListener('click', () => {
        editZone(zone)
        infoWindow.close()
      })
    }

    // View Details button
    const viewButton = document.getElementById(`map-view-zone-${zone.id}`)
    if (viewButton) {
      viewButton.addEventListener('click', () => {
        activeTab.value = 'zones'
        // Find the expansion item for this zone and expand it
        // (This will be handled by DOM after the tab change)
        infoWindow.close()
      })
    }
  })
}

// Methods for shipment assignment
function showAssignShipmentDialog(zone) {
  shipmentDialog.value.zone = zone
  shipmentDialog.value.show = true
}

async function assignShipment(shipmentId, zoneId) {
  try {
    await zoneStore.assignShipmentToZone(shipmentId, zoneId)
    shipmentDialog.value.show = false
    $q.notify({
      type: 'positive',
      message: 'Shipment assigned to zone'
    })
  } catch (error) {
    console.error('Error assigning shipment:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to assign shipment'
    })
  }
}

async function unassignShipment(shipmentId) {
  try {
    await zoneStore.unassignShipmentFromZone(shipmentId)
    $q.notify({
      type: 'info',
      message: 'Shipment unassigned from zone'
    })
  } catch (error) {
    console.error('Error unassigning shipment:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to unassign shipment'
    })
  }
}

// Methods for driver assignment
function showAssignDriverDialog(zone) {
  driverDialog.value.zone = zone
  driverDialog.value.show = true
}

async function assignDriver(driverId, zoneId) {
  try {
    await zoneStore.assignDriverToZone(driverId, zoneId)
    driverDialog.value.show = false
    $q.notify({
      type: 'positive',
      message: 'Driver assigned to zone'
    })
  } catch (error) {
    console.error('Error assigning driver:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to assign driver'
    })
  }
}

async function unassignDriver(driverId) {
  try {
    await zoneStore.unassignDriverFromZone(driverId)
    $q.notify({
      type: 'info',
      message: 'Driver unassigned from zone'
    })
  } catch (error) {
    console.error('Error unassigning driver:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to unassign driver'
    })
  }
}

// Zone CRUD operations
function createNewZone() {
  router.push('/zones/create')
}

function editZone(zone) {
  router.push(`/zones/create?id=${zone.id}`)
}

function confirmDeleteZone(zone) {
  deleteDialog.value.zone = zone
  deleteDialog.value.show = true
}

async function deleteZone() {
  if (!deleteDialog.value.zone) return

  const zoneId = deleteDialog.value.zone.id

  try {
    await zoneStore.deleteZone(zoneId)
    deleteDialog.value.show = false

    // Also refresh the all zones map
    if (activeTab.value === 'map') {
      displayAllZonesOnMap()
    }

    $q.notify({
      type: 'positive',
      message: 'Zone deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting zone:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to delete zone'
    })
  }
}

// Load data
async function loadData() {
  isLoadingData.value = true
  try {
    await zoneStore.loadAllData()

    // If we're on the map tab, initialize or refresh the map
    if (activeTab.value === 'map' && allZonesMap.value) {
      displayAllZonesOnMap()
    }
  } catch (error) {
    console.error('Error loading data:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to load data'
    })
  } finally {
    isLoadingData.value = false
  }
}

// Watch for tab changes
watch(activeTab, async (newTab) => {
  if (newTab === 'map') {
    await nextTick()
    // Always re-initialize the map when switching to map tab
    // This ensures it works properly regardless of previous state
    setTimeout(async () => {
      const mapElement = document.getElementById('all-zones-map')
      if (mapElement) {
        // Clear any existing map
        if (allZonesMap.value) {
          Object.values(allZonesPolygons.value).forEach(polygon => {
            polygon.setMap(null)
          })
          allZonesPolygons.value = {}
        }
        
        // Force re-initialization
        allZonesMap.value = null
        await initAllZonesMap()
      }
    }, 200) // Delay to ensure tab transition is complete
  }
})

// Lifecycle hooks
onMounted(async () => {
  await loadData()

  // Initialize the map if we start on the map tab
  if (activeTab.value === 'map') {
    initAllZonesMap()
  }
})

onBeforeUnmount(() => {
  // Clean up mini-maps
  Object.values(miniMapInstances.value).forEach(map => {
    // Google Maps doesn't have a direct destroy method, but we can remove the DOM element
    const element = map.getDiv()
    if (element && element.parentNode) {
      element.parentNode.removeChild(element)
    }
  })

  // Clean up all zones map
  if (allZonesMap.value) {
    // Close any open info windows
    if (activeInfoWindow.value) {
      activeInfoWindow.value.close()
    }

    // Clear polygons
    Object.values(allZonesPolygons.value).forEach(polygon => {
      polygon.setMap(null)
    })
    allZonesPolygons.value = {}
  }
})
</script>

<template>
  <q-page padding>
    <div class="q-pa-md">
      <div class="row items-center justify-between q-mb-md">
        <div class="text-h5">Delivery Zone Management</div>
        <div>
          <q-btn
            color="primary"
            icon="add_circle"
            label="Create New Zone"
            @click="createNewZone"
          />
        </div>
      </div>

      <!-- Tabs -->
      <q-tabs
        v-model="activeTab"
        class="text-primary q-mb-md"
        active-color="primary"
        indicator-color="primary"
        align="justify"
      >
        <q-tab name="map" label="Map View" icon="public" />
        <q-tab name="zones" label="Zones" icon="map" />
        <q-tab name="shipments" label="Unassigned Shipments" icon="local_shipping" />
        <q-tab name="drivers" label="Unassigned Drivers" icon="drive_eta" />
      </q-tabs>

      <q-tab-panels v-model="activeTab" animated>
        <!-- Map View Tab (NEW) -->
        <q-tab-panel name="map">
          <q-card class="q-mb-md">
            <q-card-section class="q-pa-sm">
              <div class="row items-center q-mb-sm">
                <div class="col-12">
                  <div class="text-subtitle1">All Delivery Zones</div>
                  <p class="q-my-xs text-grey-8">Click on a zone to see details and options.</p>
                </div>
              </div>
            </q-card-section>

            <!-- Full Map Container -->
            <div id="all-zones-map" class="all-zones-map"></div>

            <!-- Zones Legend -->
            <q-card-section v-if="zones.length > 0">
              <div class="text-subtitle2 q-mb-sm">Zones Legend:</div>
              <div class="row q-col-gutter-sm">
                <div class="col-6 col-sm-4 col-md-3" v-for="zone in zones" :key="zone.id">
                  <q-item dense class="q-pa-none">
                    <q-item-section avatar class="q-ml-none">
                      <q-badge :style="`background-color: ${zone.color}`" rounded style="width: 20px; height: 20px;" />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label class="text-body2 ellipsis">{{ zone.name }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </div>
              </div>
            </q-card-section>

            <!-- Empty state -->
            <q-card-section v-if="zones.length === 0" class="text-center q-pa-lg">
              <q-icon name="map" size="4em" color="grey" />
              <p class="text-h6 q-mt-md">No zones created yet</p>
              <p class="q-mb-lg">Create your first delivery zone by clicking the "Create New Zone" button.</p>
              <q-btn color="primary" label="Create New Zone" icon="add_circle" @click="createNewZone" />
            </q-card-section>
          </q-card>
        </q-tab-panel>

        <!-- Zones Tab -->
        <q-tab-panel name="zones">
          <q-list bordered separator>
            <q-expansion-item
              v-for="zone in zones"
              :key="zone.id"
              :label="zone.name"
              :caption="zone.description"
              expand-separator
              header-class="bg-grey-2"
            >
              <q-card>
                <q-card-section>
                  <!-- Zone Preview -->
                  <div class="row items-center q-mb-md">
                    <div class="col text-subtitle1">Zone Preview</div>
                    <div class="col-auto">
                      <q-btn flat dense round color="primary" icon="edit" @click="editZone(zone)" />
                      <q-btn flat dense round color="negative" icon="delete" @click="confirmDeleteZone(zone)" />
                    </div>
                  </div>

                  <!-- Map Preview -->
                  <div class="map-preview-container">
                    <MiniMapPreview :zone="zone" />
                  </div>

                  <!-- Zone Details -->
                  <div class="row q-col-gutter-md q-mt-md">
                    <div class="col-12 col-md-4">
                      <q-item>
                        <q-item-section avatar>
                          <q-icon name="palette" :color="zone.color" />
                        </q-item-section>
                        <q-item-section>
                          <q-item-label caption>Color</q-item-label>
                          <q-item-label>
                            <q-chip :style="`background-color: ${zone.color}`" text-color="white" square>
                              {{ zone.color }}
                            </q-chip>
                          </q-item-label>
                        </q-item-section>
                      </q-item>
                    </div>

                    <div class="col-12 col-md-4">
                      <q-item>
                        <q-item-section avatar>
                          <q-icon name="local_shipping" />
                        </q-item-section>
                        <q-item-section>
                          <q-item-label caption>Shipments</q-item-label>
                          <q-item-label>{{ getShipmentsForZone(zone.id).length }}</q-item-label>
                        </q-item-section>
                      </q-item>
                    </div>

                    <div class="col-12 col-md-4">
                      <q-item>
                        <q-item-section avatar>
                          <q-icon name="person" />
                        </q-item-section>
                        <q-item-section>
                          <q-item-label caption>Drivers</q-item-label>
                          <q-item-label>{{ getDriversForZone(zone.id).length }}</q-item-label>
                        </q-item-section>
                      </q-item>
                    </div>
                  </div>

                  <!-- Assigned Shipments -->
                  <div class="q-mt-md">
                    <div class="row items-center q-mb-sm">
                      <div class="col text-subtitle1">Assigned Shipments</div>
                      <div class="col-auto">
                        <q-btn flat dense color="primary" label="Assign" icon="add" @click="showAssignShipmentDialog(zone)" />
                      </div>
                    </div>

                    <q-list bordered separator>
                      <q-item v-for="shipment in getShipmentsForZone(zone.id)" :key="shipment.id">
                        <q-item-section>
                          <q-item-label>{{ shipment.trackingNumber }}</q-item-label>
                          <q-item-label caption>{{ formatAddress(shipment.destinationAddress) }}</q-item-label>
                          <q-item-label caption v-if="shipment.shipmentStatus">{{ shipment.shipmentStatus.name }}</q-item-label>
                        </q-item-section>
                        <q-item-section side>
                          <q-btn
                            flat
                            round
                            color="negative"
                            icon="close"
                            size="sm"
                            @click="unassignShipment(shipment.id)"
                          />
                        </q-item-section>
                      </q-item>

                      <q-item v-if="getShipmentsForZone(zone.id).length === 0">
                        <q-item-section>
                          <q-item-label class="text-grey">No shipments assigned to this zone</q-item-label>
                        </q-item-section>
                      </q-item>
                    </q-list>
                  </div>

                  <!-- Assigned Drivers -->
                  <div class="q-mt-md">
                    <div class="row items-center q-mb-sm">
                      <div class="col text-subtitle1">Assigned Drivers</div>
                      <div class="col-auto">
                        <q-btn flat dense color="primary" label="Assign" icon="add" @click="showAssignDriverDialog(zone)" />
                      </div>
                    </div>

                    <q-list bordered separator>
                      <q-item v-for="driver in getDriversForZone(zone.id)" :key="driver.id">
                        <q-item-section avatar>
                          <q-avatar color="primary" text-color="white">
                            {{ (driver.firstName ? driver.firstName.charAt(0) : driver.username.charAt(0)).toUpperCase() }}
                          </q-avatar>
                        </q-item-section>
                        <q-item-section>
                          <q-item-label>{{ driver.firstName && driver.lastName ? `${driver.firstName} ${driver.lastName}` : driver.username }}</q-item-label>
                          <q-item-label caption>{{ driver.contactNumber || driver.epodEmail }}</q-item-label>
                          <q-item-label caption v-if="driver.vehicleLicensePlate">{{ driver.vehicleLicensePlate }}</q-item-label>
                        </q-item-section>
                        <q-item-section side>
                          <q-btn
                            flat
                            round
                            color="negative"
                            icon="close"
                            size="sm"
                            @click="unassignDriver(driver.id)"
                          />
                        </q-item-section>
                      </q-item>

                      <q-item v-if="getDriversForZone(zone.id).length === 0">
                        <q-item-section>
                          <q-item-label class="text-grey">No drivers assigned to this zone</q-item-label>
                        </q-item-section>
                      </q-item>
                    </q-list>
                  </div>
                </q-card-section>
              </q-card>
            </q-expansion-item>

            <div v-if="zones.length === 0" class="text-center q-pa-xl">
              <q-icon name="map" size="4em" color="grey" />
              <p class="text-h6 q-mt-md">No zones created yet</p>
              <p class="q-mb-lg">Create your first delivery zone by clicking the "Create New Zone" button.</p>
              <q-btn color="primary" label="Create New Zone" icon="add_circle" @click="createNewZone" />
            </div>
          </q-list>
        </q-tab-panel>

        <!-- Shipments Tab -->
        <q-tab-panel name="shipments">
          <div class="row items-center q-mb-md">
            <div class="col text-subtitle1">Unassigned Shipments</div>
          </div>

          <q-list bordered separator>
            <q-item v-for="shipment in unassignedShipments" :key="shipment.id">
              <q-item-section>
                <q-item-label>{{ shipment.trackingNumber }}</q-item-label>
                <q-item-label caption>{{ formatAddress(shipment.destinationAddress) }}</q-item-label>
                <q-item-label caption v-if="shipment.shipmentStatus">{{ shipment.shipmentStatus.name }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-btn-dropdown color="primary" label="Assign to Zone">
                  <q-list>
                    <q-item
                      v-for="zone in zones"
                      :key="zone.id"
                      clickable
                      v-close-popup
                      @click="assignShipment(shipment.id, zone.id)"
                    >
                      <q-item-section>
                        <q-item-label>{{ zone.name }}</q-item-label>
                      </q-item-section>
                      <q-item-section side>
                        <q-badge :style="`background-color: ${zone.color}`" />
                      </q-item-section>
                    </q-item>
                  </q-list>
                </q-btn-dropdown>
              </q-item-section>
            </q-item>

            <div v-if="unassignedShipments.length === 0" class="text-center q-pa-lg">
              <q-icon name="check_circle" size="3em" color="positive" />
              <p class="text-h6 q-mt-md">All shipments are assigned</p>
              <p>There are no unassigned shipments at the moment.</p>
            </div>
          </q-list>
        </q-tab-panel>

        <!-- Drivers Tab -->
        <q-tab-panel name="drivers">
          <div class="row items-center q-mb-md">
            <div class="col text-subtitle1">Unassigned Drivers</div>
          </div>

          <q-list bordered separator>
            <q-item v-for="driver in unassignedDrivers" :key="driver.id">
              <q-item-section avatar>
                <q-avatar color="primary" text-color="white">
                  {{ (driver.firstName ? driver.firstName.charAt(0) : driver.username.charAt(0)).toUpperCase() }}
                </q-avatar>
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ driver.firstName && driver.lastName ? `${driver.firstName} ${driver.lastName}` : driver.username }}</q-item-label>
                <q-item-label caption>{{ driver.contactNumber || driver.epodEmail }}</q-item-label>
                <q-item-label caption v-if="driver.vehicleLicensePlate">{{ driver.vehicleLicensePlate }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-btn-dropdown color="primary" label="Assign to Zone">
                  <q-list>
                    <q-item
                      v-for="zone in zones"
                      :key="zone.id"
                      clickable
                      v-close-popup
                      @click="assignDriver(driver.id, zone.id)"
                    >
                      <q-item-section>
                        <q-item-label>{{ zone.name }}</q-item-label>
                      </q-item-section>
                      <q-item-section side>
                        <q-badge :style="`background-color: ${zone.color}`" />
                      </q-item-section>
                    </q-item>
                  </q-list>
                </q-btn-dropdown>
              </q-item-section>
            </q-item>

            <div v-if="unassignedDrivers.length === 0" class="text-center q-pa-lg">
              <q-icon name="check_circle" size="3em" color="positive" />
              <p class="text-h6 q-mt-md">All drivers are assigned</p>
              <p>There are no unassigned drivers at the moment.</p>
            </div>
          </q-list>
        </q-tab-panel>
      </q-tab-panels>
    </div>

    <!-- Assign Shipment Dialog -->
    <q-dialog v-model="shipmentDialog.show" persistent>
      <q-card style="min-width: 350px; max-width: 600px">
        <q-card-section class="row items-center">
          <div class="text-h6">Assign Shipments to {{ shipmentDialog.zone?.name }}</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section class="q-pt-none">
          <q-list bordered separator>
            <q-item v-for="shipment in unassignedShipments" :key="shipment.id" clickable @click="assignShipment(shipment.id, shipmentDialog.zone?.id)">
              <q-item-section>
                <q-item-label>{{ shipment.trackingNumber }}</q-item-label>
                <q-item-label caption>{{ formatAddress(shipment.destinationAddress) }}</q-item-label>
                <q-item-label caption v-if="shipment.shipmentStatus">{{ shipment.shipmentStatus.name }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-btn flat round color="primary" icon="add" />
              </q-item-section>
            </q-item>

            <q-item v-if="unassignedShipments.length === 0">
              <q-item-section>
                <q-item-label class="text-grey">No unassigned shipments available</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Close" color="primary" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- Assign Driver Dialog -->
    <q-dialog v-model="driverDialog.show" persistent>
      <q-card style="min-width: 350px; max-width: 600px">
        <q-card-section class="row items-center">
          <div class="text-h6">Assign Drivers to {{ driverDialog.zone?.name }}</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section class="q-pt-none">
          <q-list bordered separator>
            <q-item v-for="driver in unassignedDrivers" :key="driver.id" clickable @click="assignDriver(driver.id, driverDialog.zone?.id)">
              <q-item-section avatar>
                <q-avatar color="primary" text-color="white">
                  {{ (driver.firstName ? driver.firstName.charAt(0) : driver.username.charAt(0)).toUpperCase() }}
                </q-avatar>
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ driver.firstName && driver.lastName ? `${driver.firstName} ${driver.lastName}` : driver.username }}</q-item-label>
                <q-item-label caption>{{ driver.contactNumber || driver.epodEmail }}</q-item-label>
                <q-item-label caption v-if="driver.vehicleLicensePlate">{{ driver.vehicleLicensePlate }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-btn flat round color="primary" icon="add" />
              </q-item-section>
            </q-item>

            <q-item v-if="unassignedDrivers.length === 0">
              <q-item-section>
                <q-item-label class="text-grey">No unassigned drivers available</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Close" color="primary" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- Delete Confirmation Dialog -->
    <q-dialog v-model="deleteDialog.show" persistent>
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="warning" color="negative" text-color="white" />
          <span class="q-ml-sm">Are you sure you want to delete zone "{{ deleteDialog.zone?.name }}"?</span>
        </q-card-section>

        <q-card-section>
          <p>This will also unassign all shipments and drivers from this zone.</p>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Cancel" color="primary" v-close-popup />
          <q-btn flat label="Delete" color="negative" @click="deleteZone" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- Loading Overlay -->
    <q-inner-loading :showing="loading || isLoadingData">
      <q-spinner-gears size="50px" color="primary" />
    </q-inner-loading>
  </q-page>
</template>

<style scoped>
.map-preview-container {
  width: 100%;
  height: 200px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  border: 1px solid #ddd;
}

.all-zones-map {
  width: 100%;
  height: 500px;
  border-radius: 0;
  overflow: hidden;
  position: relative;
}

@media (max-width: 599px) {
  .map-preview-container {
    height: 150px;
  }

  .all-zones-map {
    height: 350px;
  }
}

/* Info window styling */
:deep(.zone-info-window) {
  padding: 5px;
  max-width: 300px;
}

:deep(.zone-info-window h3) {
  margin-top: 0;
  margin-bottom: 5px;
}

:deep(.zone-info-window .zone-actions) {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
}
</style>
