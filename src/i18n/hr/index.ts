// This is just an example,
// so you can safely delete all default props below

export default {
  submit: "Spremi",
  all: "Svi",
  AutomaticQRscan: "Automatsko skeniranje QR koda",
  personalSettings: "Osobne postavke",
  appSettings: "Postavke aplikacije",
  addressBook: "<PERSON>men<PERSON>",
  contactCarrier: "Kontaktiraj Poslodavca (prijevoznika)",
  languageSelector: "Jezik",
  saveSettings: "Spremi Postavke",
  blue: "plava",
  purple: "ljubičasta",
  red: "crvena",
  dark: "tamna",
  brown: "smedja",
  newAppVersion: "Preuzmi novu verziju aplikacije",
  settings: "Postavke",
  myDetails: "Moji detalji",
  myOrders: "<PERSON>ji <PERSON>",
  search: "Pretrazi",
  notExecuted: "Neizvrseni",
  inProgress: "U radu",
  executed: "Izvrsen<PERSON>",
  shipmentId: "Id pošiljke",
  shippingAgent: "Špediter",
  executor: "<PERSON>zv<PERSON><PERSON><PERSON><PERSON><PERSON>",
  loadingDate: "Datum utovara",
  loadingPlace: "Mje<PERSON> utovara",
  deliveryDate: "Datum istovara",
  pickupDate: "Datum istovara",
  deliveryPlace: "Mjesto istovara",
  orderNumber: "Broj naloga",
  showOrderStatus: "Prikaži status pošiljke",
  sidesInvolved: "Ukljucene strane",
  goodsDetails: "Roba detalji",
  orderInfo: "Detalji Naloga",
  actions: "Akcije",
  closeOrder: "Zatvori Nalog",
  forOrderCloseNeedOrderScan: "Za zatvaranje naloga potreban je POD scan!",
  carrier: "Prijevoznik",
  driver: "Vozac",
  owner: "Vlasnik",
  package: "Paket",
  description: "Opis",
  type: "Tip",
  dimensions: "Dimenzije",
  weight: "Težina",
  note: "Napomena",
  specialCargo: "Poseban Teret",
  download: "Preuzmi",
  donwloadPartiesAndPackage: "Preuzmi uključene strane i detalji paketa.",
  downloadOrder: "Preuzmi nalog",
  share: "Podjeli",
  sendEmail: "Pošalji E-mail",
  start: "Zapocni",
  orderStatus: "Status Pošiljke",
  delivering: "Dostavlja se...",
  awaitingDelivery: "Čeka se isporuka...",
  awaitingLoad: "Čeka se utovar...",
  destionationAddressNotAssigned: "Niste zadali adresu odredišta!",
  loading: "Utovar ",
  shipmentLoadedOnLocation: "Posiljka utovarena na lokaciji: ",
  inTransport: "U transportu",
  shipmentInTransport: "Pošiljka je u transportu",
  shipmentDeliveredOnDestination:
    "Posiljka je istovarena/pretovarena na lokaciji istovara: ",
  waitingForShipmentConfirmation: "Čeka se potvrda pošiljke...",
  finalShipmentAddress: "Konačna adresa istovara/pretovara: ",
  unloading: "Istovar ",
  routes: "Rute",
  whereToDrive: "Gdje treba voziti?",
  from: "Od",
  to: "Do",
  preparedRoutes: "Spremljene rute",
  addRoute: "Dodaj rutu",
  youCanAddRoutesHere: "Ovdje se mogu dodavati rute kojima se ide!",
  prepareRouteForTransport: "Spremi rutu za prijevoz!",
  writeRouteName: "Napisi ime rute:",
  addLocations: "Dodaj lokacije:",
  saveRoute: "Sačuvaj rutu",
  name: "Ime",
  freeEpod: "Slobodni ePOD",
  dailyViewSentEPOD: " Dnevni pregled poslanih POD-ova",
  sentPODs: "Poslani POD-ovi",
  PodNo: "Pod broj",
  todayNoSentPODDocs: "*Danas nemate poslanih POD dokumenata.",
  attachments: "Prilozi",
  info: "info",
  receivers: "Primatelji",
  PodDocNumber: "Broj Pod dokumenta",
  PleaseScanPodDocBeforeSending: "*Molim skenirajte POD dokument prije slanja",
  shipmentNumber: "Broj pošiljke: ",
  reference: "Referenca",
  shippingLoading: "OTPREMA (utovar):",
  deliveryUnloading: "DOSTAVA (istovar): ",
  sender: "POŠILJATELJ",
  receiver: "PRIMATELJ:",
  quantity: "Količina (koll): ",
  volume: "Volumen (m3): ",
  email: "E-mail",
  sendEpodToMyEmail: "Pošalji ePOD na moj e-mail",
  emailForSendingPODToEmployer: "E-mail za slanje POD-a poslodavcu",
  emailForSendingPOD: "E-mail za slanje POD-a ",
  employerEmail: "E-mail nalogodavca",
  send: "Pošalji",
  unknown: "NEPOZNAT",
  ePodNo: "ePOD broj",
  removePicture: "Ukloni sliku",
  areYouSure: "Da li Ste sigurni?",
  gpsSetting: "Postavke gps",
  toUseScannerEnableGps:
    "Za koristenje ePOD skenera potrebno je dozvoliti gps!",
  locationSettings: "Postavke lokacije",
  cameraSettings: "Postavke kamere",
  toUseScannerAllowCameraInSettings:
    "Za korištenje ePOD skenera potrebno je dozvoliti kameru u postavkama!",
  removeDocs: "Izbriši dokumente",
  areYouSureYouWantToRemoveScannedDocs:
    "Jeste li sigurni da želite izbrisati sve skenirane dokumente ?",
  forSendingYouNeedInternetTurnOnDataOrWifi:
    "Za slanje je potrebna internet veza. Uključite mobilne podatke ili se spojite na wifi!",
  validatinEpod: "Validacija ePODA u tijeku...",
  danger: "Oprez!",
  orderManagment: "Upravljanje nalozima",
  saveCSVOrder: "Spremi CSV nalog",
  addNewOrder: "Novi nalog",
  searchByDriverUsername: "Pretraži po korisničkom imenu vozača",
  addDriver: "Dodaj Vozača",
  changeDriverInformation: "Promjena informacija vozača",
  driverSuccesfullyRemoved: "Vozač uspješno obrisan",
  errorDriverDetele: "Greška pri brisanju vozača.",
  driverManagement: "Upravljanje Vozačima",
  newDriver: "Novi Vozač",
  username: "Korisničko ime",
  lastName: "Prezime",
  ePODemail: "EPOD E-mail",
  contactNumber: "Kontaktni broj",
  editDetailsDelete: "Uredi  Detalji  Obriši",
  addUser: "Dodaj korisnika",
  driverSuccesfullyCreated: "Vozač uspješno kreiran!",
  driverNotCreated: "Vozač nije kreiran. Netočni podaci u formi.",
  driverSuccesfullyEdited: "Izmjena uspjesna!",
  driverNotEdited: "Izmjena nije uspijela. Netocni podaci u formi.",
  back: "Nazad",
  viewDriver: "Pregled Vozača",
  users: "Korisnici",
  admin: "Administrator",
  office: "Ured",
  passwordSuccesfullySent: "Uspješno poslana nova lozinka!",
  errorSendingPassword: "Greska prilikom slanja nove lozinke.",
  userSuccesfudllyCreated: "Korisnik uspješno dodan.",
  userUnsuccesfullyCreated: "Dodavanje korisnika nije uspjelo",
  userSuccesfulySubmited: "Korisnik uspješno spremljen!",
  userUnsuccesfulySubmited:
    "Korisnik neuspješno spremljen. Netočni podaci u formi.",
  newUserData: "Podaci o novom korisniku.",
  carrierDrivers: "Vozači prijevoznika",
  shippingAgentCarriers: "Prijevoznici Špeditera",
  addTempUserPassword:
    "Dodaj privremenu lozinku korisniku (ovo će obrisati trenutnu lozinku ovog korisnika)",
  setPassword: "Postavi lozinku",
  initalPassword: "Inicijalna lozinka",
  vehicleRegistration: "Registracija vozila",
  emailForEPOD: "E-mail za slanje EPOD-a",
  selectRoles: "Izaberi uloge",
  addingUser: "Dodavanje korisnika",
  roles: "Uloge",
  companyId: "Id tvrtke",
  shipmentManagement: "Upravljanje pošiljkama",
  map: "Karta",
  scanner: "Skener",
  shippingAgentOrders: "Upravljanje nalozima",
  companies: "Tvrtke",
  globalSettings: "Globalne postavke",
  documents: "Dokumenti",
  check: "Provjera",
  appearence: "Izgled",
  login: "Prijava",
  logout: "Odjava",
  noLoggedInUser: "Korisnik nije prijavljen",
  youNeedInternetConnectoin:
    "Ispričavamo se, ali morate biti spojeni na internet",
  areYouSureYouWantToLeave: "Jeste li sigurni da zelite izaci?",
  shipmentsOrders: "Pošiljke - nalozi",
  trackingNumber: "Broj za praćenje pošiljke",
  status: "Status",
  dateOfCollectedDelivery: "Datum preuzimanja isporuke",
  dateOfDelivery: "Datum isporuke",
  assignDrivers: "Dodijeli vozače",
  shipmentDetails: "Detalji pošiljke",
  orderName: "Ime naloga",
  dateOfCollectingDelivery: "Datum preuzimanja isporuke",
  shipmentStatus: "Status Pošiljke",
  shipmentExecutor: "Izvršioc shipmenta",
  loadingAddress: "Adresa preuzimanja",
  unloadingAddress: "Adresa isporuke",
  tbd: "TBD", // to be delivered,
  assignOrder: "Dodjela naloga",
  dateOfShipmentPickup: "Datum utovara pošiljke",
  dateOfShipmentUnload: "Datum istovara pošiljke",
  orderDeletedSuccesfuly: "Uspjesno obrisan nalog!",
  order: "Nalog",
  orderExecutor: "Izvršioc naloga",
  close: "Zatvori",
  street: "Ulica",
  sourceAddress: "Izvorna adresa",
  city: "Grad",
  zipCode: "Poštanski broj",
  country: "Zemlja",
  obligatoryField: "Obavezno polje!",
  editShipment: "Izmjeni posiljku",
  shipmentUpdatedSuccessfully: "Pošiljka uspješno izmenjena!",
  failedToUpdateShipment: "Neuspjšena izmjena pošiljke. Netočni podaci.",
  assignShipmentDrivers: "Dodjeli vozače za pošiljku",
  carrierName: "Naziv prijevoznika",
  assignDriverOrS: "Dodjeli Vozača/e",
  driverAssignedSuccesfuly: "Vozač uspješno dodjeljen!",
  driverAssignedUnseccesfuly: "Vozač neuspješno dodjeljen. Netočni podaci",
  toUseGoogleMapsYouNeedInternet:
    "Ispričavamo se, ali da bi koristili Google Maps moratei biti povezani na internet",
  youAreHere: "Vi se nalazite ovde",
  location: "Lokacija",
  notLoggedIn: "Niste prijavljeni!",
  logginToSeeRoutes: "Login to add and see your favourite routes",
  areYouSureYouWantToRemoveRoute:
    "Da li ste sigurni da želite obrisati ovu rutu?",
  removeRoute: "Ukloni rutu",
  choosePreparedRoute: "Izaberi spremljenu rutu!",
  searchRadious: "Pretraga po radijusu",
  kilometers: "Kilometara",
  gotoMap: "Idi na mapu",
  offline: "Offline",
  yourRouteOffline:
    "Vasa ruta ne moze biti dodana. Provjerite jeste li spojeni na internet.",
  writeErrorMsg: "Napisi ime za ovu rutu",
  emailSentConfimrAccDelete:
    "Email je poslan. Potvrdite brisanje naloga klikom na link na email-u.",
  errorContactAdmin:
    "Dogodila se pogreška. Pokušajte ponovo kasnije ili kontaktirajte administratora.",
  profile: "Profil",
  unknownRole: "Nepoznata uloga",
  passwordSetting: "Podešavanje lozinke",
  oldPassword: "Stara/inicijalna lozinka",
  newPassword: "Nova lozinka",
  repeatPassword: "Ponovi lozinku",
  dataReady: "Podaci spremljeni!",
  emailSent: "E-mail je poslan!",
  errorOccured: "Došlo je do pogreške",
  contact: "contact",
  additionalEmail: "Dodatan E-mail",
  title: "Naslov",
  accountDeleted: "Obrisan nalog",
  profileSuccesfulyRemoved: "Vaš profil je uspješno obrisan",
  employer: "Poslodavac",
  currentDriverLocation: "Trenutna lokacija vozača",
  isSharingCurrentLocation: "dijeli svoju trenutnu lokaciju",
  showOnMap: "Prikaz na karti",
  locationInfoSent: "Info o lokaciji je poslan!",
  locationInfoNotSent:
    "Info o lokaciji nije poslan. Potrebno je prihvatiti koristenje trenutne aplikacije i ukljuciti gps",
  sendYourLocation: "Pošalji svoju lokaciju",
  chooseEmailDownloadLocation:
    "Odaberite odakle će se povući e-mail za slanje lokacije:",
  fromOrder: "Iz naloga",
  free: "Slobodno",
  locationWillBeSentToEmailAddresses:
    "Vaša lokacija će biti poslana na sljedeće e-mail adrese:",
  pickOrder: "Odaberi nalog",
  destinationAddress: "Adresa istovara",
  destination: "Odrediste",
  ordersNotExistant: "Nalozi ne postoje",
  yourLocationWillBeSentToEmailAddressess:
    "Vaša lokacija će biti poslana na sljedeće email adrese",
  driverDetails: "Detalji vozača",
  documentCode: "Šifra dokumenta",
  name2: "Naziv",
  creationDate: "Datum kreiranja",
  showPdf: "Prikaz(pdf)",
  carrierDetails: "Detalji Prijevoznika",
  resetingPassword: "Reset lozinke",
  emailPasswordToken:
    "Unesite Vaš e-mail, novu lozinku i reset token koji ste primili na Vaš e-mail",
  emailAddress: "E-mail adresa",
  passwordResetToken: "Token za reset lozinke",
  confirm: "Potvrdi",
  rememberPassowrd: "Zaboravio si lozinku?",
  success: "Uspjesno",
  failure: "Neuspjesno",
  errorSendingRequest: "Dogodila se greška prilikom slanja zahtjeva",
  checkYourInfo: "Provjerite unjete podatke",
  register: "Registrirajte se",
  registerFreeTrial:
    "Registriraj se sada i dobiti ces 1 mjesec besplatnog probnog perioda",
  welcomeToEpod: "Dobrodošli u ePOD aplikaciju",
  loginFailed: "Vas login nije uspješan. Provjerite vaše detalje.",
  signIn: "Prijavi se",
  signInToAccount: "Prijavi se u svoj racun",
  didForgetPassword: "Zaboravio si lozinku?",
  forgotPassword: "Zaboravljena lozinka",
  entarEmailtoResetPasswrd: "Enter your email address to reset your password",
  company: "Tvrtka",
  legalPerson: "Pravna osoba",
  taxOib: "OIB",
  mbs: "MBS",
  phone: "Telefon",
  partnerCompanyCreatedSuccesfully: "Partner tvrtka uspješno spremljena!",
  partnerCompanyCreatedUnSuccesfully:
    "Partner tvrtka neuspješno spremljena. Netočni podaci u formi.",
  addPartner: "Dodaj partnera",
  updatePartner: 'Izmijeni partnera',
  partnerName: "Ime partnera",
  VatNumber: "VAT broj",
  openCamera: "Otvori kameru",
  takePhoto: "Slikaj",
  result: "Rezultat",
  camera: "Kamera",
  paperHighlightTresh: "Naglasak papira (Threshold)",
  paperHighlightCany: "Naglasak papira (Canny)",
  scanQrCodeOnPod: "Skeniraj QR kod na POD-u",
  podDocNotLoadedPress:
    "Broj POD dokumenta nije učitan - molim ručno upišite broj POD dokumenta ili pritisnite",
  andScanQrDoc: "i skenirajte QR kod na dokumentu.",
  scanQrCodeOnDoc: "Skeniraj QR kod na POD-u",
  yourLocationIsDifferentFromUnloadingAddress:
    "Vaša lokacija se razlikuje od očitane adrese istovara {addressFoundVar} za {distanceFromShipmentAddressVar} km zračne udaljenosti.",
  unloadDateDifferentFromTodayDate:
    "Datum istovara {podInfoVar} se razlikuje od današnjeg datuma! ",
  pressSendToStartSending: "Pritisnite POŠALJI za početak slanja. ",
  cancel: "Odustani",
  sending: "Slanje u tijeku",
  sendingStoppedNeedEmail:
    "Slanje prekinuto! Treba postojati barem jedna email adresa!",
  pictureProcessing: "Obrada slika je u tijeku. Molimo pričekajte...",
  OCRSuccess: "OCR uspješan!",
  shipment: "Pošiljka",
  QRFound: "Qr code pronađen",
  epodSuccesfullySend: "ePOD uspješno poslan!",
  referentialNo: "Referentni broj",
  deliveryCompany: "Firma dostave",
  deliveryCompanyAddress: "Adresa od firme dostave",
  cargoDesription: "Opis robe",
  amount: "Količina (koll)",
  weightKg: "Težina (kg)",
  ldm: "ldm",
  readDeliveryAddress: "Očitana adresa istovara",
  airDistanceTruckFromDelivery: "Zračna udaljenost kamiona od adrese istovara",
  errorOccuredTryAgain: "Dogodila se greška. Pokušaj ponovno.",
  userDeletedSuccesfully: "Uspješno obrisan korisnik!.",
  activationEmail: "Aktivacijski E-mail",
  passwordResetActivationEmailSend:
    "Resetiraj lozinku i pošalji aktivacijski email?",
  routeDisplay: "Prikaz ruta",
  openNavigation: "Otvori navigaciju",
  startLocation: "Početna lokacija",
  noAddedPoints: "Nemate dodanih dodatnih ponintov-a!",
  addPoints: "Dodajte pointo-ve na vasu rutu!",
  companyDeletedSuccesfully: "Uspjesno obrisana tvrtka.",
  fullName: "Puno ime",
  carrierSuccesfullyDelete: "Prijevoznik uspješno obrisan!",
  carrierUnsuccesfullyDelete: "Greška pri brisanju Prijevoznika.",
  carrierMenagement: "Upravljanje Prijevoznicima",
  newCarrier: "Novi prijevoznik",
  carrierSuccesfullyCreated: "Prijevoznik uspješno kreiran!",
  carrierUnsuccesfullyCreated:
    "Prijevoznik nije kreiran. Netočni podaci u formi.",
  carrierSuccesfullyEdited: "Prijevoznik uspješno izmjenjen!",
  carrierUnsuccesfullyEdited:
    "Prijevoznik nije izmjenjen. Netočni podaci u formi.",
  ePodEmail: "ePOD E-mail",
  boxDynamite: "Dinamit u kutijama",
  explosiv: "Eksploziv",
  carefulAirCondition: "Pazljivo rukovati! Pali klimu",
  shipmentNo: "Broj transportnog naloga",
  shipmentType: "Vrsta transportnog naloga",
  orderer: "Nalogodavac",
  organization: "Organizacija",
  pickupLocation: "Lokacija preuzimanja",
  destinationLocation: "Lokacija odredista",
  locationDateTime: "Odrediste datum i vrijeme",
  orderIsPdf: "Nalog je u PDF formatu",
  copyLink: "Kopiraj link",
  sendUsingEmail: "Pošalji putem e-maila",
  scanEPod: "Skeniraj ePOD",
  chooseCloseOrder: "Odaberite nalog za zatvaranje.",
  searchExpression: "Izraz za pretraživanje",
  workOnOrderNoStart: "Zapoceti rad na nalogu broj",
  orderPdf: "Pdf naloga",
  orderType: "Tip naloga",
  created: "Datum kreiranja",
  btn: "btn",
  new: "Novi",
  companyUsers: "Tvrke korisnika",
  orderName2: "Naziv naloga",
  carrierOrCarriers: "Prijevoznik/ci",
  shippingFormFor: "Forma za pošiljke",
  passToAgent: "Proslijedi drugom agentu",
  assignCarroerAndShipment: "Dodijeli prijevoznika i shipment",
  assignAgent: "Dodijeli špeditera",
  assignShipmentCarrier: "Dodijeli prijevoznika pošiljke",
  orderAssignedSuccesfully: "Nalog uspješno dodjeljen!",
  orderAssignedUnsuccesfully: "Nalog neuspješno dodeljen. Netočni podaci forme",
  shipementAndCarrierAssignedSuccesfully:
    "Shipment i prijevoznik uspješno dodeljeni!",
  shipementAndCarrierAssignedUnsuccesfully:
    "Shipment i prijevoznik neuspješno dodeljeni. Netočni podaci u formi.",
  orderCapture: "Slikaj nalog",
  sourcePickupAddress: "Početna adresa preuzimanja",
  finalDestinationAddress: "Krajnja adresa dostave",
  date: "Datum",
  fetchOibData: "Dohvati podatke preko Oib-a",
  fetchMbsData: "Dohvati podatke preko Mbs-a",
  fetchEoriData: "Provjeri EORI",
  fetchViesData: "Provjeri VIES",
  fetchViesDataHistory: "Prikaži VIES povijest provjera",
  externalId: "Eksterni Id",
  searchByOrderName: "Pretraži po imenu naloga",
  searchByCarrierUsername: "Pretraži po korisničkom imenu prijevoznika",
  searchByShipmentStatus: "Pretraži po statusu",
  orderSuccesfullyCreated: "Nalog uspješno kreiran",
  orderUnsuccesfullyCreated: "Nalog nije kreiran. Netočni podaci u formi.",
  orderSubmitedSuccesfully: "Nalog uspješno spremljen",
  orderSubmitedUnuccesfully:
    "Nalog neuspjesno spremljen. Netočni podaci u formi.",
  docScannedSuccesfully: "dokument uspjesno skeniran",
  latitude: "Geografska širina",
  longitude: "Geografska dužina",
  orderDeleted: "Nalog je obrisan!",
  cantDeleteOrderWhileShipmenStatusActive:
    "Nemoguće obrisati nalog dok se podnalog izvršava",
  succesfullyAddedOrders: "Uspjesno dodat/i nalog/zi!",
  errorAddingOrder: "Greška u dodavanju naloga",
  emailNotSent: "E-mail nije poslan! Dogodila se greska.",
  addOrder: "Novi nalog",
  shippingAgents: "Prijevoznici",
  orderCreationDate: "Datum kreiranja naloga",
  orderCompletionDate: "Datum kompletiranja naloga",
  carrierForShipmentNotFound: "Prijevoznik za ovu pošiljku nije pronađen.",
  orderReceivedFromAgent: "Nalog dobijen od agenta",
  orderCreatedByYou: "Nalog je kreiran od Vas",
  orderForwarded: "Nalog prosledjen",
  orderForwardedToYouExecutor:
    "Nalog je dodjeljen Vama. Vi ste izvršioc naloga.",
  agents: "Agenti",
  selectLanguage: "Izaberi jezik",
  edit: "Uredi",
  delete: "Obriši",
  password: "Lozinka",
  settingsUpdated: "Postavke spremljene!",
  settingsUpdateFailed: "Dogodila se pogreška! Postavke nisu spremljene",
  pickupDetails: "Utovar",
  deliveryDetails: "Istovar",
  externalReference: "Referenca",
  notes: "Napomena",
  goodsType: "Vrsta robe",
  parcel: "Broj koleta",
  numberOfEURPalettes: "Broj EUR paleta",
  numberOfXPalettes: "Broj X paleta",
  signedDocuments: "Potpisani dokumenti",
  deliveryType: "Tip dostave",
  ransom: "Otkupnina",
  uploadFile: "Ucitaj datoteku",
  documentScanner: "Skeniraj nalog",
  orderDetails: "Detalji naloga",
  dsvIdentifier: "DSV identifikator",
  aiExtraction: "AI ekstrakcija podataka",
  enableAIExtraction: "Omogući AI ekstrakciju podataka",
  dataExtractedSuccessfully: "Podaci uspješno ekstrahirani",
  errorExtractingData: "Greška pri ekstrakciji podataka",
  errorScanning: "Greška pri skeniranju dokumenta",
  processingDocument: "Obrada dokumenta...",
  aiProcessing: "AI obrada...",
  extractedPartnerName: "Ekstragirano ime partnera",
  extractedPartnerNameInfo: "Ovo ime je ekstrahirano iz skeniranog dokumenta. Za korištenje ovog partnera, odaberite odgovarajućeg partnera iz padajućeg izbornika ili kreirajte novog.",
  pickupAddress: "Adresa utovara",

  NOT_DANGEROUS: "Nije opasno",
  EXPLOSIVES: "Eksplozivi",
  GASES: "Plinovi",
  OXIDISING_SUBSTANCES: "Oxidacijske tvari",
  FLAMMABLE_LIQUID: "Zapaljive tekućine",
  FLAMMABLE_SOLIDS: "Zapaljivi čvrsti materijali",
  TOXIC_SUBSTANCES: "Toksicne tvari",
  RADIOACTIVE_MATERIAL: "Radioaktivni materijal",
  CORROSIVE_SUBSTANCES: "Korozivne tvari",
  MISCELLANEOUS_DANGEROUS_GOODS: "Razno opasno",
  adrClass: "ADR klasa",

  emailOfficeDeliveryVerification: 'Verifikacijski email za provjeru isporućenosti ePoda',
  extractionSettings: "Postavke ekstrakcije",
  configureDocumentFieldMappings: "Konfigurirajte mapiranja polja dokumenta za AI ekstrakciju",
  documentFieldName: "Naziv polja dokumenta",
  mapToFormField: "Mapiraj na polje obrasca",
  addMapping: "Dodaj mapiranje",
  currentMappings: "Trenutna mapiranja",
  noMappingsConfigured: "Još nema konfiguriranih mapiranja",
  mapsTo: "Mapira se na",
  OdgovornaOsoba: "Odgovorna Osoba",
  Kontakt: "Kontakt",
  palleteType: "Tip palete",
  visina: "Visina",
  duzina: "Dužina",
  sirina: "Širina",
  LDM: "Dužni metar (LDM)",
  lDM: "Dužni metar (LDM)",
  da: "Da",
  ne: "Ne",
  otkupnina: "Otkupnina",
  isPovratnaAmbalaza: "Povratna ambalaža",
  "povratna ambalaza": "Povratna ambalaža",
  partner: "Partner",
  contactPerson: "Kontakt osoba",
  notSelected: "Nije odabrano",
  forward: "Proslijedi",
  orderStatusId: "Status naloga",
  customPrompt: "Prilagođeni upit",
  customPromptLabel: "Unesite prilagođeni upit za AI",
  customPromptHelp: "Unesite prilagođeni upit koji će biti poslan AI-u za ekstrakciju podataka. Ovo će prepisati zadani upit.",
  save: "Spremi",

  // New translations for AssignDriverComponent
  singleDriver: "Jedan vozač",
  cmr: "CMR",
  selectDriver: "Odaberi vozača",
  executors: "izvršitelje",
  splitBetween: "Podijeli između",
  splitShipment: "Podijeli pošiljku",
  of: "od",
  useOriginalDestination: "Koristi originalnu destinaciju",
  shippingNumber: "Broj pošiljke",
  assignmentMode: "Način dodjele",
  assignSingleDriver: "Dodijeli jednog vozača",
  usePreviousDestination: "Koristi prethodnu destinaciju",
  arrivedAtPickup: "Stigao na utovar",
  pickedUp: "Utovaren",
  arrivedAtDelivery: "Stigao na istovar",
  useLocationForAutoTracking: "Koristi lokaciju za automatsko praćenje",
  automaticallyUpdateStatusBasedOnLocation: "Automatski ažuriraj status na temelju lokacije",
  scanPOD: "Skeniraj POD",
  finishDelivery: "Završi dostavu",
  scanEpodToCompleteOrder: "Skeniraj ePOD za završetak naloga",
  scanPODAndFinish: "Skeniraj POD i završi",
  availableAtDeliveryLocation: "Dostupno na lokaciji istovara",
  completed: "Završeno",
  tapToAdvance: "Dodirnite za nastavak",
  tapToGoBack: "Dodirnite za prethodni",
  currentStatus: "Trenutni status",
  noDataAvailable: "Nema dostupnih podataka",
  errorFetchingCompanyData: 'Greška pri dohvaćanju podataka tvrtke. Provjerite identifikacijski broj i pokušajte ponovno.',
  invalidOIB: 'Neispravan format OIB-a. OIB mora imati 11 znamenki s ispravnom kontrolnom znamenkom.',
  invalidMBS: 'Neispravan format MBS-a. MBS mora imati 8 ili 9 znamenki.',
  viesValidationSuccessful: 'PDV broj uspješno provjeren.',
  viesValidationFailed: 'Provjera PDV broja nije uspjela. Provjerite broj i državu.',
  confirmDelete: 'Potvrdi brisanje',
  confirmDeletePartnerMessage: 'Jeste li sigurni da želite izbrisati ovog partnera? Ova radnja se ne može poništiti.',
  errorDeletingPartner: 'Greška pri brisanju partnera. Pokušajte ponovo.',
  partnerDeletedSuccessfully: 'Partner uspješno izbrisan.',
  zoneManagement: "Upravljanje zonama",
  zone: "Zona",
  assign: "Dodijeli",
  assignDriver: "Dodijeli vozača",
  noZone: "Nema zone",
  zonesManagement: "Upravljanje zonama",
  pickupOpeningHours: "Vremenski period za utovar",
  deliveryOpeningHours: "Vremenski period za istovar",
  openFrom: "Otvoren od",
};
