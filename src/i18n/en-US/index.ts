export default {
  submit: 'Save',
  all: 'All',
  AutomaticQRscan: 'Automatic QR scan from image',
  personalSettings: 'Personal Settings',
  appSettings: 'App Settings',
  addressBook: 'Address Book',
  contactCarrier: 'Contact Carrier',
  languageSelector: 'Language',
  saveSettings: 'Save Settings',
  blue: 'Blue',
  purple: 'Purple',
  red: 'Red',
  dark: 'Dark',
  brown: 'Brown',
  newAppVersion: 'Download New App Version',
  settings: 'Settings',
  myDetails: 'My Details',
  myOrders: 'My Orders',
  search: 'Search',
  notExecuted: 'Not Executed',
  inProgress: 'In Progress',
  executed: 'Executed',
  shipmentId: 'Shipment ID',
  shippingAgent: 'Shipping Agent',
  executor: 'Executor',
  loadingDate: 'Loading Date',
  loadingPlace: 'Loading Place',
  deliveryDate: 'Delivery Date',
  deliveryPlace: 'Delivery Place',
  orderNumber: 'Order Number',
  showOrderStatus: 'Show Order Status',
  sidesInvolved: 'Sides Involved',
  goodsDetails: 'Goods Details',
  orderInfo: 'Order Info',
  actions: 'Actions',
  closeOrder: 'Close Order',
  forOrderCloseNeedOrderScan: 'For order closure, POD scan is required!',
  carrier: 'Carrier',
  driver: 'Driver',
  owner: 'Owner',
  package: 'Package',
  normalGoods: 'Normal Goods',
  description: 'Description',
  type: 'Type',
  dimensions: 'Dimensions',
  weight: 'Weight',
  note: 'Note',
  specialCargo: 'Special Cargo',
  download: 'Download',
  donwloadPartiesAndPackage: 'Download included parties and package details.',
  waitingForShipmentConfirmation: 'Waitig to confirm delivery of the shipment...',
  downloadOrder: 'Download Order',
  share: 'Share',
  sendEmail: 'Send Email',
  start: 'Start',
  orderStatus: 'Order Status',
  delivering: 'Delivering...',
  awaitingDelivery: 'Awaiting Delivery...',
  awaitingLoad: 'Awaiting Load...',
  destionationAddressNotAssigned: 'Destination Address Not Assigned',
  loading: 'Loading',
  shipmentLoadedOnLocation: 'Shipment loaded at location: ',
  inTransport: 'In Transport',
  shipmentInTransport: 'Shipment is in transport',
  shipmentDeliveredOnDestination: 'Shipment delivered at destination location: ',
  finalShipmentAddress: 'Final shipment address: ',
  unloading: 'Unloading',
  routes: 'Routes',
  whereToDrive: 'Where to Drive?',
  from: 'From',
  to: 'To',
  preparedRoutes: 'Prepared Routes',
  addRoute: 'Add Route',
  youCanAddRoutesHere: 'You can add routes here!',
  prepareRouteForTransport: 'Prepare Route for Transport!',
  writeRouteName: 'Write Route Name:',
  addLocations: 'Add Locations:',
  saveRoute: 'Save Route',
  name: 'Name',
  freeEpod: 'Free ePOD',
  dailyViewSentEPOD: 'Daily View of Sent ePODs',
  sentPODs: 'Sent PODs',
  PodNo: 'POD Number',
  todayNoSentPODDocs: '*You have no sent POD documents today.',
  attachments: 'Attachments',
  info: 'Info',
  receivers: 'Receivers',
  PodDocNumber: 'POD Document Number',
  PleaseScanPodDocBeforeSending: '*Please scan POD document before sending',
  shipmentNumber: 'Shipment Number: ',
  reference: 'Reference:',
  shippingLoading: 'SHIPPING (Loading):',
  deliveryUnloading: 'DELIVERY (Unloading): ',
  sender: 'SENDER',
  recevier: 'RECEIVER:',
  quantity: 'Quantity (pcs): ',
  volume: 'Volume (m3): ',
  email: 'Email',
  sendEpodToMyEmail: 'Send ePOD to my email',
  emailForSendingPODToEmployer: 'Email for sending POD to employer',
  emailForSendingPOD: 'Email for sending POD',
  employerEmail: 'Employer Email',
  send: 'Send',
  unknown: 'Unknown',
  ePodNo: 'ePOD Number',
  removePicture: 'Remove Picture',
  areYouSure: 'Are You Sure?',
  gpsSetting: 'GPS Settings',
  toUseScannerEnableGps: 'To use the ePOD scanner, GPS permission is required!',
  locationSettings: 'Location Settings',
  cameraSettings: 'Camera Settings',
  toUseScannerAllowCameraInSettings: 'To use the ePOD scanner, camera permission is required in settings!',
  removeDocs: 'Remove Documents',
  areYouSureYouWantToRemoveScannedDocs: 'Are you sure you want to remove all scanned documents?',
  forSendingYouNeedInternetTurnOnDataOrWifi: 'For sending, an internet connection is required. Turn on mobile data or connect to Wi-Fi!',
  validatinEpod: 'Validating ePOD in progress...',
  danger: 'Warning',
  orderManagment: 'Order Management',
  saveCSVOrder: 'Save CSV Order',
  addNewOrder: 'Add New Order',
  searchByDriverUsername: 'Search by Driver Username',
  addDriver: 'Add Driver',
  changeDriverInformation: 'Change Driver Information',
  driverSuccesfullyRemoved: 'Driver successfully removed',
  errorDriverDetele: 'Error deleting driver.',
  driverManagement: 'Driver Management',
  newDriver: 'New Driver',
  username: 'Username',
  lastName: 'Last Name',
  ePODemail: 'EPOD Email',
  contactNumber: 'Contact Number',
  editDetailsDelete: 'Edit Details Delete',
  addUser: 'Add User',
  edit: 'Edit',
  driverSuccesfullyCreated: 'Driver successfully created!',
  driverNotCreated: 'Driver not created. Incorrect data in the form.',
  driverSuccesfullyEdited: 'Driver successfully edited!',
  driverNotEdited: 'Driver not successfully edited. Incorrect data in the form.',
  back: 'Back',
  viewDriver: 'View Driver',
  users: 'Users',
  admin: 'Administrator',
  office: 'Office',
  passwordSuccesfullySent: 'New password successfully sent!',
  errorSendingPassword: 'Error sending new password.',
  userSuccesfudllyCreated: 'User successfully created.',
  userUnsuccesfullyCreated: 'User unsuccessfully created.',
  userSuccesfulySubmited: 'User successfully submitted!',
  userUnsuccesfulySubmited: 'User unsuccessfully submitted. Incorrect data in the form.',
  newUserData: 'New User Data.',
  carrierDrivers: 'Carrier Drivers',
  shippingAgentCarriers: 'Shipping Agent Carriers',
  addTempUserPassword: 'Add temporary password to user (this will delete the current password of this user)',
  setPassword: 'Set Password',
  initalPassword: 'Initial Password',
  vehicleRegistration: 'Vehicle Registration',
  emailForEPOD: 'Email for EPOD Sending',
  selectRoles: 'Select Roles',
  addingUser: 'Adding User',
  roles: 'Roles',
  companyId: 'Company Id',
  shipmentManagement: 'Shipment Management',
  map: 'Map',
  scanner: 'Scanner',
  shippingAgentOrders: 'Shipping Agent Orders',
  companies: 'Companies',
  globalSettings: 'Global Settings',
  documents: 'Documents',
  check: 'Check',
  appearence: 'Appearance',
  login: 'Login',
  logout: 'Logout',
  noLoggedInUser: 'No logged-in user',
  youNeedInternetConnectoin: 'Sorry, but you need to be connected to the internet',
  areYouSureYouWantToLeave: 'Are you sure you want to leave?',
  shipmentsOrders: 'Shipments - Orders',
  trackingNumber: 'Tracking Number',
  status: 'Status',
  dateOfCollectedDelivery: 'Date of Collected Delivery',
  dateOfDelivery: 'Date of Delivery',
  assignDrivers: 'Assign Drivers',
  shipmentDetails: 'Shipment Details',
  orderName: 'Order Name',
  dateOfCollectingDelivery: 'Date of Collecting Delivery',
  shipmentStatus: 'Shipment Status',
  shipmentExecutor: 'Shipment Executor',
  loadingAddress: 'Loading Address',
  unloadingAddress: 'Unloading Address',
  tbd: 'To Be Delivered',
  assignOrder: 'Assign Order',
  dateOfShipmentUnload: 'Date of Shipment Unload',
  orderDeletedSuccesfuly: 'Order successfully deleted!',
  order: 'Order',
  orderExecutor: 'Order Executor',
  close: 'Close',
  street: 'Street',
  sourceAddress: 'Source Address',
  city: 'City',
  zipCode: 'Zip Code',
  country: 'Country',
  obligatoryField: 'Obligatory Field!',
  editShipment: 'Edit Shipment',
  shipmentUpdatedSuccessfully: 'Shipment successfully updated!',
  failedToUpdateShipment: 'Failed to update shipment. Incorrect form data.',
  assignShipmentDrivers: 'Assign drivers for shipment',
  carrierName: 'Carrier Name',
  assignDriverOrS: 'Assign Driver/s',
  driverAssignedSuccesfuly: 'Driver successfully assigned!',
  driverAssignedUnseccesfuly: 'Driver unsuccessfully assigned. Incorrect form data',
  toUseGoogleMapsYouNeedInternet: 'Sorry, but to use Google Maps you need to be connected to the internet',
  youAreHere: 'You are here',
  location: 'Location',
  notLoggedIn: 'Not logged in!',
  logginToSeeRoutes: 'Login to add and see your favorite routes',
  areYouSureYouWantToRemoveRoute: 'Are you sure you want to remove this route?',
  removeRoute: 'Remove Route',
  choosePreparedRoute: 'Choose prepared route!',
  searchRadious: 'Search Radius',
  kilometers: 'Kilometers',
  gotoMap: 'Go to Map',
  offline: 'Offline',
  yourRouteOffline: 'Your route cannot be added. Check if you are connected to the internet.',
  writeErrorMsg: 'Write a name for this route',
  emailSentConfimrAccDelete: 'Email sent. Confirm account deletion by clicking on the link in the email.',
  errorContactAdmin: 'An error occurred. Please try again later or contact the administrator.',
  profile: 'Profile',
  unknownRole: 'Unknown Role',
  passwordSetting: 'Password Setting',
  oldPassword: 'Old/Initial Password',
  newPassword: 'New Password',
  repeatPassword: 'Repeat Password',
  dataReady: 'Data saved!',
  emailSent: 'Email sent!',
  errorOccured: 'An error occurred',
  contact: 'Contact',
  additionalEmail: 'Additional Email',
  title: 'Title',
  accountDeleted: 'Account Deleted',
  profileSuccesfulyRemoved: 'Your profile has been successfully deleted',
  employer: 'Employer',
  currentDriverLocation: 'Current Driver Location',
  isSharingCurrentLocation: 'is sharing their current location',
  showOnMap: 'Show on Map',
  locationInfoSent: 'Location info sent!',
  locationInfoNotSent: 'Location info not sent. You need to accept using current application and enable GPS',
  sendYourLocation: 'Send your location',
  chooseEmailDownloadLocation: 'Choose where to download the location email from:',
  fromOrder: 'From order',
  free: 'Free',
  locationWillBeSentToEmailAddresses: 'Your location will be sent to the following email addresses:',
  pickOrder: 'Pick Order',
  destinationAddress: 'Destination Address',
  destination: 'Destination',
  ordersNotExistant: 'Orders do not exist',
  yourLocationWillBeSentToEmailAddressess: 'Your location will be sent to the following email addresses',
  driverDetails: 'Driver Details',
  documentCode: 'Document Code',
  name2: 'Name',
  creationDate: 'Creation Date',
  showPdf: 'Show (pdf)',
  carrierDetails: 'Carrier Details',
  resetingPassword: 'Reset Password',
  emailPasswordToken: 'Enter Your Email, New Password, and Reset Token You Received on Your Email',
  emailAddress: 'Email Address',
  passwordResetToken: 'Password Reset Token',
  confirm: 'Confirm',
  rememberPassowrd: 'Remember Password?',
  success: 'Success',
  failure: 'Failure',
  errorSendingRequest: 'An error occurred while sending the request',
  checkYourInfo: 'Check the information you entered',
  register: 'Register',
  registerFreeTrial: 'Register now and get 1 month free trial',
  welcomeToEpod: 'Welcome to the ePOD application',
  loginFailed: 'Your login was unsuccessful. Please check your details.',
  signIn: 'Sign In',
  signInToAccount: 'Sign in to your account',
  didForgetPassword: 'Forgot your password?',
  forgotPassword: 'Forgot Password',
  entarEmailtoResetPasswrd: 'Enter your email address to reset your password',
  company: 'Company',
  legalPerson: 'Legal Person',
  taxOib: 'Tax OIB',
  mbs: 'MBS',
  phone: 'Phone',
  partnerCompanyCreatedSuccesfully: 'Partner company successfully saved!',
  partnerCompanyCreatedUnSuccesfully: 'Partner company unsuccessfully saved. Incorrect data in the form.',
  addPartner: 'Add Partner',
  updatePartner: 'Update Partner',
  partnerName: 'Partner Name',
  VatNumber: 'VAT Number',
  openCamera: 'Open Camera',
  takePhoto: 'Take Photo',
  result: 'Result',
  camera: 'Camera',
  paperHighlightTresh: 'Paper Highlight (Threshold)',
  paperHighlightCany: 'Paper Highlight (Canny)',
  scanQrCodeOnPod: 'Scan QR Code on POD',
  podDocNotLoadedPress: 'POD document number not loaded - please manually enter the POD document number or press',
  andScanQrDoc: 'and scan the QR code on the document.',
  scanQrCodeOnDoc: 'Scan QR Code on POD',
  yourLocationIsDifferentFromUnloadingAddress: 'Your location is different from the detected unloading address { addressFoundVar } for { distanceFromShipmentAddressVar } km air distance.',
  unloadDateDifferentFromTodayDate: "Unload date { podInfoVar } is different from today\'s date! ",
  pressSendToStartSending: 'Press SEND to start sending. ',
  cancel: 'Cancel',
  sending: 'Sending in progress',
  sendingStoppedNeedEmail: 'Sending stopped! At least one email address is required!',
  pictureProcessing: 'Picture processing in progress. Please wait...',
  OCRSuccess: 'OCR successful!',
  shipment: 'Shipment',
  QRFound: 'QR code found',
  epodSuccesfullySend: 'ePOD successfully sent!',
  referentialNo: 'Referential number',
  deliveryCompany: 'Delivery company',
  deliveryCompanyAddress: 'Delivery company address',
  cargoDesription: 'Cargo description',
  amount: 'Quantity (koll)',
  weightKg: 'Weight (kg)',
  ldm: 'ldm',
  readDeliveryAddress: 'Read delivery address',
  airDistanceTruckFromDelivery: 'Air distance truck from delivery address',
  errorOccuredTryAgain: 'Something went wrong. Please try again.',
  userDeletedSuccesfully: 'User successfully deleted!.',
  activationEmail: 'Activation Email',
  passwordResetActivationEmailSend: 'Reset password and send activation email?',
  routeDisplay: 'Route display',
  openNavigation: 'Open navigation',
  startLocation: 'Start location',
  noAddedPoints: 'No additional points added!',
  addPoints: 'Add points to your route!',
  companyDeletedSuccesfully: 'Company successfully deleted.',
  fullName: 'Full name',
  carrierSuccesfullyDelete: 'Carrier successfully deleted!',
  carrierUnsuccesfullyDelete: 'Error deleting Carrier.',
  carrierMenagement: 'Carrier Management',
  newCarrier: 'New carrier',
  carrierSuccesfullyCreated: 'Carrier successfully created!',
  carrierUnsuccesfullyCreated: 'Carrier not created. Incorrect data in the form.',
  carrierSuccesfullyEdited: 'Carrier successfully edited!',
  carrierUnsuccesfullyEdited: 'Carrier not edited. Incorrect data in the form.',
  ePodEmail: 'ePOD Email',
  boxDynamite: 'Dynamite in boxes',
  explosiv: 'Explosive',
  carefulAirCondition: 'Handle with care! Turn on the air conditioning',
  shipmentNo: 'Shipment number',
  shipmentType: 'Shipment type',
  orderer: 'Orderer',
  organization: 'Organization',
  pickupLocation: 'Pickup location',
  destinationLocation: 'Destination location',
  locationDateTime: 'Destination date and time',
  orderIsPdf: 'Order is in PDF format',
  copyLink: 'Copy link',
  sendUsingEmail: 'Send via email',
  scanEPod: 'Scan ePOD',
  chooseCloseOrder: 'Choose the order to close.',
  searchExpression: 'Search expression',
  workOnOrderNoStart: 'Work on order number started',
  orderPdf: 'Order PDF',
  orderType: 'Order type',
  created: 'Created',
  btn: 'btn',
  new: 'New',
  companyUsers: 'Company users',
  orderName2: 'Order name',
  carrierOrCarriers: 'Carrier/s',
  shippingFormFor: 'Shipping form for',
  passToAgent: 'Pass to another agent',
  assignCarroerAndShipment: 'Assign carrier and shipment',
  assignAgent: 'Assign agent',
  assignShipmentCarrier: 'Assign shipment carrier',
  orderAssignedSuccesfully: 'Order successfully assigned!',
  orderAssignedUnsuccesfully: 'Order unsuccessfully assigned. Incorrect form data',
  shipementAndCarrierAssignedSuccesfully: 'Shipment and carrier successfully assigned!',
  shipementAndCarrierAssignedUnsuccesfully: 'Shipment and carrier unsuccessfully assigned. Incorrect form data.',
  orderCapture: 'Capture order',
  sourcePickupAddress: 'Source pickup address',
  finalDestinationAddress: 'Final destination address',
  date: 'Date',
  fetchOibData: 'Get data using Oib',
  fetchMbsData: 'Get data using Mbs',
  fetchEoriData: "Check EORI",
  fetchViesData: "Check VIES",
  fetchViesDataHistory: "View VIES history",
  externalId: 'External Id',
  searchByOrderName: 'Search by order name',
  searchByCarrierUsername: 'Search by carrier username',
  searchByShipmentStatus: 'Search by shipment status',
  orderSuccesfullyCreated: 'Order succesfully created',
  orderUnsuccesfullyCreated: 'Order not created. Incorrect data in the form.',
  orderSubmitedSuccesfully: 'Order successfully saved',
  orderSubmitedUnuccesfully: 'Order unsuccessfully saved. Incorrect data in the form.',
  docScannedSuccesfully: 'Document successfully scanned',
  latitude: 'Latitude',
  longitude: 'Longitude',
  orderDeleted: 'Order has been deleted!',
  cantDeleteOrderWhileShipmenStatusActive: 'Unable to delete order while shipment is processing',
  succesfullyAddedOrders: 'Successfully added order/orders!',
  errorAddingOrder: 'Error adding order',
  emailNotSent: 'E-mail not sent! An error occured.',
  addOrder: 'Add Order',
  orderCreationDate: 'Order Created',
  orderCompletionDate: 'Order Finished',
  carrierForShipmentNotFound: 'The carrier for this ahipment was not found',
  orderReceivedFromAgent: 'Order received from agent',
  orderCreatedByYou: 'The order was created by you',
  orderForwarded: 'Order Forwarded',
  orderForwardedToYouExecutor: 'The order was forwarded to you. You are the order executor.',
  agents: 'Agents',
  settingsUpdated: 'Settings updated!',
  settingsUpdateFailed: 'Update failed. There was an error',
  selectLanguage: 'Select Language',
  password: 'Password',

  aiExtraction: "AI Data Extraction",
  enableAIExtraction: "Enable AI data extraction",
  dataExtractedSuccessfully: "Data extracted successfully",
  errorExtractingData: "Error extracting data",
  errorScanning: "Error scanning document",
  processingDocument: "Processing document...",
  aiProcessing: "AI processing...",

  extractedPartnerName: "Extracted Partner Name",

  NOT_DANGEROUS: "Not Dangerous",
  explosives: "Explosives",
  gases: "Gases",
  flammableLiquid: "Flammable Liquid",
  flammableSolids: "Flammable Solids",
  oxidisingSubstances: "Oxidising Substances",
  toxicSubstances: "Toxic Substances",
  radioactiveMaterial: "Radioactive Material",
  corrosiveSubstances: "Corrosive Substances",
  miscellaneousDangerousGoods: "Miscellaneous Dangerous Goods",
  adrClass: "ADR class",

  extractedPartnerNameInfo: "This name was extracted from the scanned document. To use this partner, either select a matching partner from the dropdown or create a new one.",
  emailOfficeDeliveryVerification: 'Email for ePod Delivery Verification',
  extractionSettings: "Extraction Settings",
  configureDocumentFieldMappings: "Configure document field mappings for AI extraction",
  documentFieldName: "Document Field Name",
  mapToFormField: "Map to Form Field",
  addMapping: "Add Mapping",
  currentMappings: "Current Mappings",
  noMappingsConfigured: "No mappings configured yet",
  mapsTo: "Maps to",
  OdgovornaOsoba: "Responsible Person",
  Kontakt: "Contact",
  palleteType: "Pallet Type",
  visina: "Height",
  duzina: "Length",
  sirina: "Width",
  LDM: "Loading Meter (LDM)",
  da: "Yes",
  ne: "No",
  otkupnina: "Cash on Delivery",
  isPovratnaAmbalaza: "Returnable Packaging",
  "povratna ambalaza": "Returnable Packaging",
  partner: "Partner",
  contactPerson: "Contact Person",
  dsvIdentifier: "DSV Identifier",
  deliveryType: "Delivery Type",
  notSelected: "Not selected",
  forward: "Forward",
  orderStatusId: "Order Status",
  customPrompt: "Custom Prompt",
  customPromptLabel: "Enter custom prompt for AI extraction",
  customPromptHelp: "Enter a custom prompt to guide the AI extraction process. This prompt will be sent to the AI model along with the scanned document.",
  lDM: "Loading Meter (LDM)",
  // New translations for AssignDriverComponent
  singleDriver: "Single Driver",
  cmr: "CMR",
  selectDriver: "Select Driver",
  executors: "executors",
  splitBetween: "Split between",
  splitShipment: "Split Shipment",
  of: "of",
  useOriginalDestination: "Use Original Destination",
  shippingNumber: "Shipping Number",
  assignmentMode: "Assignment Mode",
  assignSingleDriver: "Assign Single Driver",
  usePreviousDestination: "Use Previous Destination",

  // Add these new translations
  arrivedAtPickup: "Arrived at pickup",
  pickedUp: "Picked up",
  arrivedAtDelivery: "Arrived at delivery",
  useLocationForAutoTracking: "Use location for auto tracking",
  automaticallyUpdateStatusBasedOnLocation: "Automatically update status based on location",
  scanPOD: "Scan POD",
  finishDelivery: "Finish Delivery",
  scanEpodToCompleteOrder: "Scan ePOD to complete order",
  scanPODAndFinish: "Scan POD and Finish",
  availableAtDeliveryLocation: "Available at delivery location",
  completed: "Completed",
  tapToAdvance: "Tap to advance",
  currentStatus: "Current Status",
  noDataAvailable: "No data available",
  errorFetchingCompanyData: 'Error fetching company data. Please check the identification number and try again.',
  invalidOIB: 'Invalid OIB format. OIB must be 11 digits with a valid check digit.',
  invalidMBS: 'Invalid MBS format. MBS must be 8 or 9 digits.',
  viesValidationSuccessful: 'VAT number successfully validated.',
  viesValidationFailed: 'VAT number validation failed. Please check the number and country.',
  confirmDelete: 'Confirm Delete',
  confirmDeletePartnerMessage: 'Are you sure you want to delete this partner? This action cannot be undone.',
  delete: 'Delete',
  errorDeletingPartner: 'Error deleting partner. Please try again.',
  partnerDeletedSuccessfully: 'Partner deleted successfully.',
  zoneManagement: 'Zone Management',
  zone: "Zone",
  assign: "Assign",
  assignDriver: "Assign Driver",
  noZone: "No Zone",
  zonesManagement: "Zones management",
}
