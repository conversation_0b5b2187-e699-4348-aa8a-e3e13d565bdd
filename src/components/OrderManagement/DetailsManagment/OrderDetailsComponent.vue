<template>
  <div class="">
    <div v-if="order" class="q-mt-md">
      <q-card flat bordered>
        <q-card-section class="bg-brand text-white">
          <div class="row items-center justify-between">
            <div>
              <div class="text-h6">{{ $t('orderDetails') }}</div>
              <div class="text-subtitle2" v-if="order.externalReference">{{ $t('externalReference') }}: {{ order.externalReference }}</div>
            </div>
            <q-btn @click="goBack" icon="close" color="white" flat />
          </div>
        </q-card-section>

        <q-card-section>
          <div class="row q-col-gutter-md">
            <!-- Basic Info Column -->
            <div class="col-12 col-md-6">
              <q-card flat bordered class="full-height">
                <q-card-section class="bg-grey-2">
                  <div class="text-subtitle1 text-weight-bold">{{ $t('basicInfo') }}</div>
                </q-card-section>

                <q-list padding>
                  <q-item>
                    <q-item-section>
                      <q-item-label caption>{{ $t('orderId') }}</q-item-label>
                      <q-item-label>{{ order.id || '-' }}</q-item-label>
                    </q-item-section>
                  </q-item>

                  <q-item v-if="order.externalReference">
                    <q-item-section>
                      <q-item-label caption>{{ $t('externalReference') }}</q-item-label>
                      <q-item-label>{{ order.externalReference }}</q-item-label>
                    </q-item-section>
                  </q-item>

                  <q-item v-if="order.trackingNumber">
                    <q-item-section>
                      <q-item-label caption>{{ $t('trackingNumber') }}</q-item-label>
                      <q-item-label>{{ order.trackingNumber }}</q-item-label>
                    </q-item-section>
                  </q-item>

                  <q-item v-if="order.dsvIdentifier">
                    <q-item-section>
                      <q-item-label caption>{{ $t('dsvIdentifier') }}</q-item-label>
                      <q-item-label>{{ order.dsvIdentifier }}</q-item-label>
                    </q-item-section>
                  </q-item>

                  <q-item v-if="order.orderType">
                    <q-item-section>
                      <q-item-label caption>{{ $t('type') }}</q-item-label>
                      <q-item-label class="flex items-center">
                        <span>{{ order.orderType?.name }}</span>
                        <q-icon v-if="order.orderTypeId && order.orderTypeId !== 1"
                                name="warning"
                                color="orange"
                                size="sm"
                                class="q-ml-xs"
                        >
                          <q-tooltip>{{ $t('dangerousGoods') }}</q-tooltip>
                        </q-icon>
                      </q-item-label>
                    </q-item-section>
                  </q-item>

                  <q-item v-if="order.orderStatus">
                    <q-item-section>
                      <q-item-label caption>{{ $t('orderStatus') }}</q-item-label>
                      <q-item-label>
                        <q-badge :color="getStatusColor(order.orderStatus)">
                          {{ order.orderStatus?.name }}
                        </q-badge>
                      </q-item-label>
                    </q-item-section>
                  </q-item>

                  <q-item v-if="order.goodsType">
                    <q-item-section>
                      <q-item-label caption>{{ $t('goodsType') }}</q-item-label>
                      <q-item-label>{{ order.goodsType }}</q-item-label>
                    </q-item-section>
                  </q-item>

                  <q-item v-if="order.parity">
                    <q-item-section>
                      <q-item-label caption>{{ $t('parity') }}</q-item-label>
                      <q-item-label>{{ order.parity }}</q-item-label>
                    </q-item-section>
                  </q-item>

                  <q-item v-if="order.responsiblePerson">
                    <q-item-section>
                      <q-item-label caption>{{ $t('responsiblePerson') }}</q-item-label>
                      <q-item-label>{{ order.responsiblePerson }}</q-item-label>
                    </q-item-section>
                  </q-item>

                  <q-item v-if="carrier">
                    <q-item-section>
                      <q-item-label caption>{{ $t('carrier') }}</q-item-label>
                      <q-item-label>{{ carrier.companyName }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </q-list>
              </q-card>
            </div>

            <!-- Dates Column -->
            <div class="col-12 col-md-6">
              <q-card flat bordered class="full-height">
                <q-card-section class="bg-grey-2">
                  <div class="text-subtitle1 text-weight-bold">{{ $t('dates') }}</div>
                </q-card-section>

                <q-list padding>
                  <q-item v-if="order.dateCreated">
                    <q-item-section>
                      <q-item-label caption>{{ $t('orderCreationDate') }}</q-item-label>
                      <q-item-label>{{ formatDate(order.dateCreated) }}</q-item-label>
                    </q-item-section>
                  </q-item>

                  <q-item v-if="order.dateUpdated">
                    <q-item-section>
                      <q-item-label caption>{{ $t('dateUpdated') }}</q-item-label>
                      <q-item-label>{{ formatDate(order.dateUpdated) }}</q-item-label>
                    </q-item-section>
                  </q-item>

                  <q-item v-if="order.datePickup">
                    <q-item-section>
                      <q-item-label caption>{{ $t('pickupDate') }}</q-item-label>
                      <q-item-label>{{ formatDate(order.datePickup) }}</q-item-label>
                    </q-item-section>
                  </q-item>

                  <q-item v-if="order.dateDelivery">
                    <q-item-section>
                      <q-item-label caption>{{ $t('deliveryDate') }}</q-item-label>
                      <q-item-label>{{ formatDate(order.dateDelivery) }}</q-item-label>
                    </q-item-section>
                  </q-item>

                  <q-item>
                    <q-item-section>
                      <q-item-label caption>{{ $t('orderCompletionDate') }}</q-item-label>
                      <q-item-label v-if="order.orderCompletionDate">{{ }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </q-list>
              </q-card>
            </div>

            <!-- Shipping Info Column -->
            <div class="col-12 col-md-6">
              <q-card flat bordered class="full-height">
                <q-card-section class="bg-grey-2">
                  <div class="text-subtitle1 text-weight-bold">{{ $t('pickupInfo') }}</div>
                </q-card-section>

                <q-list padding>
                  <q-item v-if="order.sourcePickupAddress">
                    <q-item-section>
                      <q-item-label caption>{{ $t('sourcePickupAddress') }}</q-item-label>
                      <q-item-label>{{ formatAddress(order.sourcePickupAddress) }}</q-item-label>
                    </q-item-section>
                  </q-item>

                  <q-item v-if="order.partnerNamePickup">
                    <q-item-section>
                      <q-item-label caption>{{ $t('partnerNamePickup') }}</q-item-label>
                      <q-item-label>{{ order.partnerNamePickup }}</q-item-label>
                    </q-item-section>
                  </q-item>

                  <q-item v-if="order.contactPickup">
                    <q-item-section>
                      <q-item-label caption>{{ $t('contactPickup') }}</q-item-label>
                      <q-item-label>{{ order.contactPickup }}</q-item-label>
                    </q-item-section>
                  </q-item>

                  <q-item v-if="order.contactPickupPhone">
                    <q-item-section>
                      <q-item-label caption>{{ $t('contactPickupPhone') }}</q-item-label>
                      <q-item-label>
                        <a :href="`tel:${order.contactPickupPhone}`">{{ order.contactPickupPhone }}</a>
                      </q-item-label>
                    </q-item-section>
                  </q-item>
                </q-list>
              </q-card>
            </div>

            <!-- Delivery Info Column -->
            <div class="col-12 col-md-6">
              <q-card flat bordered class="full-height">
                <q-card-section class="bg-grey-2">
                  <div class="text-subtitle1 text-weight-bold">{{ $t('deliveryInfo') }}</div>
                </q-card-section>

                <q-list padding>
                  <q-item v-if="order.finalDestinationAddress">
                    <q-item-section>
                      <q-item-label caption>{{ $t('finalDestinationAddress') }}</q-item-label>
                      <q-item-label>{{ formatAddress(order.finalDestinationAddress) }}</q-item-label>
                    </q-item-section>
                  </q-item>

                  <q-item v-if="order.partnerNameDelivery">
                    <q-item-section>
                      <q-item-label caption>{{ $t('partnerNameDelivery') }}</q-item-label>
                      <q-item-label>{{ order.partnerNameDelivery }}</q-item-label>
                    </q-item-section>
                  </q-item>

                  <q-item v-if="order.contactDelivery">
                    <q-item-section>
                      <q-item-label caption>{{ $t('contactDelivery') }}</q-item-label>
                      <q-item-label>{{ order.contactDelivery }}</q-item-label>
                    </q-item-section>
                  </q-item>

                  <q-item v-if="order.contactDeliveryPhone">
                    <q-item-section>
                      <q-item-label caption>{{ $t('contactDeliveryPhone') }}</q-item-label>
                      <q-item-label>
                        <a :href="`tel:${order.contactDeliveryPhone}`">{{ order.contactDeliveryPhone }}</a>
                      </q-item-label>
                    </q-item-section>
                  </q-item>
                </q-list>
              </q-card>
            </div>

            <!-- Cargo Details -->
            <div class="col-12">
              <q-card flat bordered class="full-height">
                <q-card-section class="bg-grey-2">
                  <div class="text-subtitle1 text-weight-bold">{{ $t('cargoDetails') }}</div>
                </q-card-section>

                <q-list padding class="row q-col-gutter-md">
                  <div class="col-12 col-md-6">
                    <q-item v-if="order.numberOfParcels !== undefined">
                      <q-item-section>
                        <q-item-label caption>{{ $t('numberOfParcels') }}</q-item-label>
                        <q-item-label>{{ order.numberOfParcels }}</q-item-label>
                      </q-item-section>
                    </q-item>

                    <q-item v-if="order.palleteType">
                      <q-item-section>
                        <q-item-label caption>{{ $t('palleteType') }}</q-item-label>
                        <q-item-label>{{ order.palleteType?.name }}</q-item-label>
                      </q-item-section>
                    </q-item>

                    <q-item v-if="order.numberOfReturnedPalletes1 !== undefined">
                      <q-item-section>
                        <q-item-label caption>{{ $t('numberOfEURPalletes') }}</q-item-label>
                        <q-item-label>{{ order.numberOfReturnedPalletes1 }}</q-item-label>
                      </q-item-section>
                    </q-item>

                    <q-item v-if="order.numberOfReturnedPalletes2 !== undefined">
                      <q-item-section>
                        <q-item-label caption>{{ $t('numberOfXPalletes') }}</q-item-label>
                        <q-item-label>{{ order.numberOfReturnedPalletes2 }}</q-item-label>
                      </q-item-section>
                    </q-item>
                  </div>

                  <div class="col-12 col-md-6">
                    <q-item v-if="order.weight">
                      <q-item-section>
                        <q-item-label caption>{{ $t('weight') }}</q-item-label>
                        <q-item-label>{{ order.weight }}</q-item-label>
                      </q-item-section>
                    </q-item>

                    <q-item v-if="order.dimensions">
                      <q-item-section>
                        <q-item-label caption>{{ $t('dimensions') }}</q-item-label>
                        <q-item-label>{{ order.dimensions }}</q-item-label>
                      </q-item-section>
                    </q-item>

                    <q-item>
                      <q-item-section>
                        <q-item-label caption>{{ $t('volume') }}</q-item-label>
                        <q-item-label v-if="order.volume" >{{ order.volume }}</q-item-label>
                      </q-item-section>
                    </q-item>

                    <q-item v-if="order.lDM !== undefined">
                      <q-item-section>
                        <q-item-label caption>{{ $t('lDM') }}</q-item-label>
                        <q-item-label>{{ order.lDM }}</q-item-label>
                      </q-item-section>
                    </q-item>
                  </div>
                </q-list>
              </q-card>
            </div>

            <!-- Additional Info -->
            <div class="col-12">
              <q-card flat bordered class="full-height" v-if="order.notes || order.ransom || order.signedDocuments !== undefined">
                <q-card-section class="bg-grey-2">
                  <div class="text-subtitle1 text-weight-bold">{{ $t('additionalInfo') }}</div>
                </q-card-section>

                <q-list padding>
                  <q-item v-if="order.notes">
                    <q-item-section>
                      <q-item-label caption>{{ $t('notes') }}</q-item-label>
                      <q-item-label>{{ order.notes }}</q-item-label>
                    </q-item-section>
                  </q-item>

                  <q-item v-if="order.ransom">
                    <q-item-section>
                      <q-item-label caption>{{ $t('ransom') }}</q-item-label>
                      <q-item-label>{{ order.ransom }}</q-item-label>
                    </q-item-section>
                  </q-item>

                  <q-item v-if="order.signedDocuments !== undefined">
                    <q-item-section>
                      <q-item-label caption>{{ $t('signedDocuments') }}</q-item-label>
                      <q-item-label>
                        <q-badge :color="order.signedDocuments ? 'positive' : 'negative'">
                          {{ order.signedDocuments ? $t('yes') : $t('no') }}
                        </q-badge>
                      </q-item-label>
                    </q-item-section>
                  </q-item>
                </q-list>
              </q-card>
            </div>
          </div>
        </q-card-section>
      </q-card>
    </div>

    <div v-else class="q-pa-md">
      <q-spinner color="primary" size="3em" />
      <div class="q-mt-sm">{{ $t('loadingOrderDetails') }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { formatAddress, formatDate } from 'src/models/FormatModels'
import IOrder from 'src/models/OrderModel'
import { useRouter } from 'vue-router'

const router = useRouter()

const props = defineProps({
  order: {
    type: Object as () => IOrder,
    required: true
  },
  carrier: {
    type: Object,
    default: null
  }
})

const order = ref<IOrder>(props.order)

function goBack() {
  router.go(-1)
}

// Function to determine badge color based on status
function getStatusColor(status: any) {
  if (!status) return 'grey'

  // Use statusId for consistent color mapping across the app
  const statusId = status.orderStatusId || status.id
  if (!statusId) return 'grey'

  // Unified status colors based on OrderStatus enum
  const statusColors: Record<number, string> = {
    1: 'yellow',        // CREATED
    2: 'blue',      // ASSIGNED
    3: 'positive',    // IN_PROGRESS
    4: 'green',        // COMPLETED
    5: 'negative',    // CANCELED
    6: 'red',         // FAILED

  }
  return statusColors[statusId] || 'grey'
}
</script>

<style scoped>
.full-height {
  height: 100%;
}
</style>
