<template>
  <q-card class="q-mb-md">
    <q-card-section>
      <!-- Only show single order details when NOT in batch mode -->
      <template v-if="order && (!multipleOrders || multipleOrders.length <= 1)">
        <div class="order-details-card q-pa-md q-mb-lg">
          <div class="row justify-between items-start q-mb-md">
            <div class="col-12 col-md-8">
              <div class="text-h6 q-mb-xs">{{ order.name || order.trackingNumber }}</div>
              <div class="text-subtitle2 text-grey-8">{{ order.externalReference || '' }}</div>
            </div>
            <div class="col-12 col-md-4 text-right">
              <q-chip :color="getOrderStatusColor(order.orderStatusId)" text-color="white" icon="assignment_add">
                {{ getOrderStatusName(order) }}
              </q-chip>
            </div>
          </div>

          <div class="row q-col-gutter-md">
            <!-- Order Details Column 1 -->
            <div class="col-12 col-md-6">
              <div class="row q-col-gutter-sm">
                <!-- Order Type -->
                <div class="col-12 col-sm-6">
                  <div class="text-caption text-grey">{{ $t('orderType') }}</div>
                  <div class="flex items-center">
                    <span>{{ order.orderType?.name || '--' }}</span>
                    <q-icon v-if="order.orderTypeId && order.orderTypeId !== 1"
                            name="warning"
                            color="orange"
                            size="sm"
                            class="q-ml-xs"
                    >
                      <q-tooltip>{{ $t('dangerousGoods') }}</q-tooltip>
                    </q-icon>
                  </div>
                </div>
                <div class="col-12 col-sm-6">
                  <div class="text-caption text-grey">{{ $t('nalogodavatelj') }}</div>
                  <div>{{ executor?.shippingAgent.companyName || '--' }}</div>
                </div>

                <!-- External Reference -->
                <div class="col-12 col-sm-6">
                  <div class="text-caption text-grey">{{ $t('externalReference') }}</div>
                  <div>{{ order.externalReference || '--' }}</div>
                </div>

                <!-- DSV Identifier -->
                <div class="col-12 col-sm-6">
                  <div class="text-caption text-grey">{{ $t('dsvIdentifier') }}</div>
                  <div>{{ order.dsvIdentifier || '--' }}</div>
                </div>

                <!-- Pickup Date -->
                <div class="col-12 col-sm-6">
                  <div class="text-caption text-grey">{{ $t('pickupDate') }}</div>
                  <div class="text-weight-medium text-primary">{{ formatDate(order.datePickup) || '--' }}</div>
                </div>

                <!-- Delivery Date -->
                <div class="col-12 col-sm-6">
                  <div class="text-caption text-grey">{{ $t('deliveryDate') }}</div>
                  <div class="text-weight-medium text-secondary">{{ formatDate(order.dateDelivery) || '--' }}</div>
                </div>

                <!-- Responsible Person -->
                <div class="col-12">
                  <div class="text-caption text-grey">{{ $t('responsiblePerson') }}</div>
                  <div>{{ order.responsiblePerson || '--' }}</div>
                </div>

                <!-- LDM if exists -->
                <div class="col-12 col-sm-6" v-if="order.responsiblePersonContact">
                  <div class="text-caption text-grey">{{ $t('responsiblePersonContact') }}</div>
                  <div>{{ order.order.responsiblePersonContact }} <q-icon name="phone" size="xs" /></div>
                </div>
              </div>
            </div>

            <!-- Order Details Column 2 -->
            <div class="col-12 col-md-6">
              <div class="row q-col-gutter-sm">
                <!-- Goods Type & Weight -->
                <div class="col-12 col-sm-6">
                  <div class="text-caption text-grey">{{ $t('goodsType') }}</div>
                  <div>{{ order.goodsType || '--' }}</div>
                </div>
                <div class="col-12 col-sm-6">
                  <div class="text-caption text-grey">{{ $t('weight') }}</div>
                  <div>{{ order.weight || '--' }} kg</div>
                </div>

                <!-- LDM & Volume (prioritize LDM) -->
                <div class="col-12 col-sm-6">
                  <div class="text-caption text-grey">{{ $t('lDM') }}</div>
                  <div>{{ order.lDM || '--' }} LDM</div>
                </div>
                <div class="col-12 col-sm-6">
                  <div class="text-caption text-grey">{{ $t('volume') }}</div>
                  <div>{{ order.volume || '--' }} m³</div>
                </div>

                <!-- Parcels Count & Pallete Info -->
                <div class="col-12 col-sm-6">
                  <div class="text-caption text-grey">{{ $t('numberOfParcels') }}</div>
                  <div>{{ order.numberOfParcels || '--' }}</div>
                </div>
                <div class="col-12 col-sm-6">
                  <div class="text-caption text-grey">{{ $t('palleteType') }}</div>
                  <div>{{ order.palleteType?.name || '--' }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Source and Destination Addresses -->
          <div class="row q-col-gutter-md q-mt-md">
            <div class="col-12 col-md-6">
              <div class="text-caption text-grey">{{ $t('sourcePickupAddress') }}</div>
              <div class="address-block q-pa-sm bg-grey-2 rounded-borders">
                {{ formatOrderAddress(order.sourcePickupAddress) }}
              </div>
            </div>
            <div class="col-12 col-md-6">
              <div class="text-caption text-grey">{{ $t('finalDestinationAddress') }}</div>
              <div class="address-block q-pa-sm bg-grey-2 rounded-borders">
                {{ formatOrderAddress(order.finalDestinationAddress) }}
              </div>
            </div>
          </div>
        </div>
      </template>


      <!-- Assignment Form -->
      <q-form ref="formRef" @submit.prevent="submitForm">
        <!-- Assignment Options -->
        <div class="q-mt-md">
          <div class="text-subtitle1 q-mb-md">{{ $t('assignmentOptions') }}</div>

          <!-- Assignment Selects Row -->
          <div class="row q-col-gutter-md q-mb-md">
            <!-- Shipping Agent Select (Optional) -->
<!--            <div class="col-12 col-md-4">-->
<!--              <div class="q-mb-xs">-->
<!--                <q-checkbox-->
<!--                  v-model="showShippingAgent"-->
<!--                  :label="$t('assignToShippingAgent')"-->
<!--                  color="brand"-->
<!--                />-->
<!--              </div>-->
<!--              <q-select-->
<!--                v-if="showShippingAgent"-->
<!--                filled-->
<!--                v-model="selectedShippingAgent"-->
<!--                :options="shippingAgents"-->
<!--                :option-value="(opt: any) => opt?.id || null"-->
<!--                :option-label="(opt: any) => opt?.companyName || ''"-->
<!--                :label="$t('selectShippingAgent')"-->
<!--                clearable-->
<!--                color="brand"-->
<!--              >-->
<!--                <template v-slot:prepend>-->
<!--                  <q-icon name="business" color="brand"/>-->
<!--                </template>-->
<!--              </q-select>-->
<!--            </div>-->

            <!-- Carrier Select (Always Visible) -->
            <div class="col-12 col-md-4">
              <div class="text-caption q-mb-xs">{{ $t('carrier') }} *</div>
              <q-select
                filled
                v-model="selectedCarrier"
                :options="carriers"
                :option-value="(opt: any) => opt"
                :option-label="(opt: any) => opt?.companyName || ''"
                :label="$t('selectCarrier')"
                :rules="[(val: any) => !!val || $t('obligatoryField')]"
                color="brand"
                @update:model-value="onCarrierChange"
              >
                <template v-slot:prepend>
                  <q-icon name="local_shipping" color="brand"/>
                </template>
              </q-select>
            </div>

            <!-- Driver Select (Optional, based on carrier) -->
            <div class="col-12 col-md-4">
              <div class="text-caption q-mb-xs">{{ $t('driver') }} ({{ $t('optional') }})</div>
              <q-select
                filled
                v-model="selectedDriver"
                :options="carrierDrivers"
                :option-value="(opt: any) => opt"
                :option-label="(opt: any) => opt ? `${opt.firstName} ${opt.lastName}` : ''"
                :label="$t('selectDriver')"
                :disable="!selectedCarrier"
                clearable
                color="brand"
              >
                <template v-slot:prepend>
                  <q-icon name="person" color="brand"/>
                </template>
              </q-select>
            </div>
          </div>
        </div>

        <!-- Shipment Details -->
        <template v-if="selectedCarrier || showShippingAgent">
          <q-expansion-item
            :label="$t('shipmentDetails')"
            header-class="text-primary"
            expand-separator
            default-opened
            class="q-my-md"
          >
            <q-card class="q-pa-md">
              <!-- 3-Column Layout for Carrier and Date Pickers -->

              <div class="row q-col-gutter-md">
<!--                <div class="col-12 col-md-3">
                  <div class="text-subtitle1 q-mb-sm">{{ $t('shipmentType') }}</div>
                  <q-btn-toggle
                    v-model="shipment.shipmentType"
                    class="my-custom-toggle"
                    no-caps
                    rounded
                    unelevated
                    toggle-color="brand"
                    color="white"
                    text-color="brand"
                    :options="[
              {label: 'Direktna', value: 'direct'},
              {label: 'Skladiste', value: 'warehouse'}
            ]"
                  />
                </div>-->
                <!-- Reserved for future fields -->
<!--                <div class="col-12 col-md-3">
                  &lt;!&ndash; This column is reserved for consistency with date pickers &ndash;&gt;
                </div>-->

                <!-- Pickup Date -->
                <div class="col-12 col-md-3">
                  <q-input
                    filled
                    v-model="shipment.pickupDate"
                    :rules="[val => !!val || $t('obligatoryField')]"
                    :label="$t('dateOfShipmentPickup').concat('*')"
                    class="q-mb-md"
                    color="brand"
                    bg-color="brand"
                  >
                    <template v-slot:prepend>
                      <q-icon color="brand" name="event" class="cursor-pointer text-black">
                        <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                          <q-date v-model="shipment.pickupDate" color="brand" mask="YYYY-MM-DD HH:mm"
                                  :options="futureDateTime" today-btn>
                            <div class="row items-center justify-end">
                              <q-btn v-close-popup :label="$t('close')" color="brand" flat/>
                            </div>
                          </q-date>
                        </q-popup-proxy>
                      </q-icon>
                    </template>
                    <template v-slot:append>
                      <q-icon color="brand" name="access_time" class="cursor-pointer text-black">
                        <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                          <q-time color="brand" v-model="shipment.pickupDate" mask="YYYY-MM-DD HH:mm" format24h>
                            <div class="row items-center justify-end">
                              <q-btn v-close-popup :label="$t('close')" color="brand" flat/>
                            </div>
                          </q-time>
                        </q-popup-proxy>
                      </q-icon>
                    </template>
                  </q-input>
                </div>

                <!-- Delivery Date -->
                <div class="col-12 col-md-3">
                  <q-input
                    filled
                    v-model="shipment.deliveryDate"
                    :rules="[val => !!val || $t('obligatoryField')]"
                    :label="$t('dateOfShipmentUnload').concat('*')"
                    class="q-mb-md"
                    color="brand"
                    bg-color="brand"
                  >
                    <template v-slot:prepend>
                      <q-icon name="event" class="cursor-pointer text-black">
                        <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                          <q-date v-model="shipment.deliveryDate" mask="YYYY-MM-DD HH:mm"
                                  :options="futureDateTime" today-btn>
                            <div class="row items-center justify-end">
                              <q-btn v-close-popup :label="$t('close')" color="brand" flat/>
                            </div>
                          </q-date>
                        </q-popup-proxy>
                      </q-icon>
                    </template>
                    <template v-slot:append>
                      <q-icon name="access_time" class="cursor-pointer text-black">
                        <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                          <q-time v-model="shipment.deliveryDate" mask="YYYY-MM-DD HH:mm" format24h>
                            <div class="row items-center justify-end">
                              <q-btn v-close-popup :label="$t('close')" color="brand" flat/>
                            </div>
                          </q-time>
                        </q-popup-proxy>
                      </q-icon>
                    </template>
                  </q-input>
                </div>
              </div>

              <!-- Opening Hours Time Ranges -->
              <div class="row q-col-gutter-md q-mt-md">
                <div class="col-12 col-md-6">
                  <div class="text-subtitle2 q-mb-sm">{{ $t('pickupOpeningHours') }}</div>
                  <div class="q-px-md">
                    <q-range
                      v-model="pickupOpeningHours"
                      :min="0"
                      :max="24"
                      :step="0.5"
                      :left-label-value="`${formatHour(pickupOpeningHours.min)}`"
                      :right-label-value="`${formatHour(pickupOpeningHours.max)}`"
                      label-always
                      switch-label-side
                      color="brand"
                      markers
                      snap
                    />
                  </div>
                  <div class="text-center text-caption text-grey q-mt-sm">
                    {{ $t('openFrom') }} {{ formatHour(pickupOpeningHours.min) }} {{ $t('to') }} {{ formatHour(pickupOpeningHours.max) }}
                  </div>
                </div>

                <div class="col-12 col-md-6">
                  <div class="text-subtitle2 q-mb-sm">{{ $t('deliveryOpeningHours') }}</div>
                  <div class="q-px-md">
                    <q-range
                      v-model="deliveryOpeningHours"
                      :min="0"
                      :max="24"
                      :step="0.5"
                      :left-label-value="`${formatHour(deliveryOpeningHours.min)}`"
                      :right-label-value="`${formatHour(deliveryOpeningHours.max)}`"
                      label-always
                      switch-label-side
                      color="brand"
                      markers
                      snap
                    />
                  </div>
                  <div class="text-center text-caption text-grey q-mt-sm">
                    {{ $t('openFrom') }} {{ formatHour(deliveryOpeningHours.min) }} {{ $t('to') }} {{ formatHour(deliveryOpeningHours.max) }}
                  </div>
                </div>
              </div>

              <!-- ----------->

              <!-- Source Address -->
              <q-separator class="q-my-md"/>
              <div class="q-mb-md">
                <div class="flex justify-between items-center q-mb-sm">
                  <div>
                    <q-icon name="map" size="md"/>
                    <span class="q-ml-md text-subtitle1">{{ $t('pickupAddress') }}</span>
                  </div>
                  <q-btn
                    flat
                    icon="sync"
                    color="brand"
                    :label="$t('sourcePickupAddress')"
                    @click="fillSourceAddress()"
                    :disable="!order?.sourcePickupAddress"
                  />
                </div>

                <InputAutoCompleteForm
                  class="q-mb-md"
                  :label="$t('from')"
                  @place-id="(event) => getPlaceDetails(event, shipment, 'pickup')"
                />

                <div class="row q-col-gutter-md">
                  <!-- Street -->
                  <div class="col-12 col-md-6">
                    <q-input
                      filled
                      type="text"
                      v-model="shipment.pickupAddress.street"
                      :label="$t('street').concat('*')"
                      :rules="[val => !!val || $t('obligatoryField')]"
                    />
                  </div>

                  <!-- City -->
                  <div class="col-12 col-md-6">
                    <q-input
                      filled
                      type="text"
                      v-model="shipment.pickupAddress.city"
                      :label="$t('city').concat('*')"
                      :rules="[val => !!val || $t('obligatoryField')]"
                    />
                  </div>

                  <!-- Postal Code -->
                  <div class="col-12 col-md-6">
                    <q-input
                      filled
                      type="text"
                      v-model="shipment.pickupAddress.postalCode"
                      :label="$t('zipCode').concat('*')"
                      :rules="[val => !!val || $t('obligatoryField')]"
                    />
                  </div>

                  <!-- Country -->
                  <div class="col-12 col-md-6">
                    <q-input
                      filled
                      type="text"
                      v-model="shipment.pickupAddress.country"
                      :label="$t('country').concat('*')"
                      :rules="[val => !!val || $t('obligatoryField')]"
                    />
                  </div>
                </div>
              </div>

              <!-- Destination Address -->
              <q-separator class="q-my-md"/>
              <div>
                <div class="flex justify-between items-center q-mb-sm">
                  <div>
                    <q-icon name="map" size="md"/>
                    <span class="q-ml-md text-subtitle1">{{ $t('destinationAddress') }}</span>
                  </div>
                  <div class="flex items-center gap-2">
                    <!-- Zone Detection Chip -->
                    <q-chip
                      v-if="detectedZone"
                      :style="`background-color: ${detectedZone.color}; color: white;`"
                      icon="location_on"
                      removable
                      @remove="detectedZone = null"
                    >
                      {{ $t('zone') }}: {{ detectedZone.name }}
                      <q-tooltip>{{ $t('autoDetectedZone') }}</q-tooltip>
                    </q-chip>
                    <q-spinner v-if="isDetectingZone" size="20px" color="brand" />
                    <q-btn
                      flat
                      icon="sync"
                      color="brand"
                      :label="$t('finalDestinationAddress')"
                      @click="fillDestinationAddress()"
                      :disable="!order?.finalDestinationAddress"
                    />
                  </div>
                </div>

                <InputAutoCompleteForm
                  class="q-mb-md"
                  :label="$t('to')"
                  @place-id="(event) => getPlaceDetails(event, shipment, 'destination')"
                />

                <div class="row q-col-gutter-md">
                  <!-- Street -->
                  <div class="col-12 col-md-6">
                    <q-input
                      filled
                      type="text"

                      v-model="shipment.destinationAddress.street"
                      :label="$t('street').concat('*')"
                      :rules="[val => !!val || $t('obligatoryField')]"
                    />
                  </div>

                  <!-- City -->
                  <div class="col-12 col-md-6">
                    <q-input
                      filled
                      type="text"
                      v-model="shipment.destinationAddress.city"
                      :label="$t('city').concat('*')"
                      :rules="[val => !!val || $t('obligatoryField')]"
                    />
                  </div>

                  <!-- Postal Code -->
                  <div class="col-12 col-md-6">
                    <q-input
                      filled
                      type="text"
                      v-model="shipment.destinationAddress.postalCode"
                      :label="$t('zipCode').concat('*')"
                      :rules="[val => !!val || $t('obligatoryField')]"
                    />
                  </div>

                  <!-- Country -->
                  <div class="col-12 col-md-6">
                    <q-input
                      filled
                      type="text"
                      v-model="shipment.destinationAddress.country"
                      :label="$t('country').concat('*')"
                      :rules="[val => !!val || $t('obligatoryField')]"
                    />
                  </div>
                </div>
              </div>
            </q-card>
          </q-expansion-item>
        </template>

        <!-- Submit Button and Loading Spinner -->
        <div class="q-mt-lg flex justify-end items-center">
          <q-spinner v-if="isLoading" size="36px" color="primary" class="q-mr-md"/>
          <q-btn type="submit" :label="$t('submit')" color="brand" :loading="isLoading"/>
        </div>
      </q-form>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch, defineEmits, defineProps } from 'vue'
import { useQuasar } from 'quasar'
import { useI18n } from 'vue-i18n'
import { useAuthStore } from 'src/stores/auth-store'
import IOrder from 'src/models/OrderModel'
import { IOrderShippingAgent } from 'src/models/OrderShippingAgentModel'
import IShipment from 'src/models/ShipmentModel'
import { IShipmentStatus } from 'src/models/LookupInterfaces'
import { User } from '../models'
import OrderShippingAgentService from 'src/services/OrderShippingAgentService'
import ShipmentService from 'src/services/ShipmentService'
import RouteService from 'src/services/GoogleService'
import InputAutoCompleteForm from 'components/Route/InputAutoCompleteForm.vue'
import IAddress from 'src/models/AddressModel'
import usersService from 'src/services/UsersService'
import { formatDate } from 'src/models/FormatModels'
import { useZoneStore } from 'src/stores/zone-store'
import { Zone } from 'src/models/ZoneModel'

const { t } = useI18n()
const $q = useQuasar()
const emit = defineEmits(['submit-success', 'validation-change'])

const userStore = useAuthStore()
const zoneStore = useZoneStore()

const props = defineProps({
  order: {
    type: Object as () => IOrder,
    required: false
  },
  carriers: {
    type: Array as () => User[],
    required: true
  },
  shipmentStatuses: {
    type: Array as () => IShipmentStatus[],
    required: true
  },
  shippingAgents: {
    type: Array as () => IOrderShippingAgent[],
    required: true
  },
  multipleOrders: {
    type: Array as () => IOrder[],
    required: false,
    default: () => []
  }
})

// Form state
const formRef = ref<any>(null)
const isLoading = ref(false)
const shipmentType = ref('direct')
const executor = ref<IOrderShippingAgent>()

// New assignment state
const showShippingAgent = ref(false)
const selectedShippingAgent = ref<IOrderShippingAgent | null>(null)
const selectedCarrier = ref<User | null>(null)
const selectedDriver = ref<User | null>(null)
const carrierDrivers = ref<User[]>([])

// Time range for opening hours (0-24 hours)
const pickupOpeningHours = ref({ min: 8, max: 17 })
const deliveryOpeningHours = ref({ min: 8, max: 17 })

// Zone detection state
const detectedZone = ref<Zone | null>(null)
const isDetectingZone = ref(false)

// Single shipment
const shipment = ref<IShipment>({
  id: '0',
  carrierId: null,
  carrier: null,
  shipmentExecutorId: null,
  orderId: null, // Will be set per order in batch processing
  estimateDeliveryDate: null,
  pickupDate: null,
  deliveryDate: null,
  cMR: undefined,
  shippingNumber: null,
  shipmentType: 'direct',
  shipmentStatusId: null,
  pickupAddressId: 0,
  pickupAddress: {
    id: '0',
    street: '',
    city: '',
    postalCode: '',
    country: '',
    latitude: '',
    longitude: ''
  },
  destinationAddressId: 0,
  destinationAddress: {
    id: '0',
    street: '',
    city: '',
    postalCode: '',
    country: '',
    latitude: '',
    longitude: ''
  },
  shipmentCarriers: null
})

const validateForm = async () => {
  if (!formRef.value) return false

  const isValid = await formRef.value.validate()
  emit('validation-change', isValid)
  return isValid
}

const formatOrderAddress = (address?: IAddress): string => {
  if (!address) return '--'

  const parts = []
  if (address.street) parts.push(address.street)
  if (address.city) parts.push(address.city)
  if (address.postalCode) parts.push(address.postalCode)
  if (address.country) parts.push(address.country)

  return parts.join(', ') || '--'
}

// Format hour for display
const formatHour = (hour: number): string => {
  const h = Math.floor(hour)
  const m = Math.round((hour - h) * 60)
  return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}`
}

// Unified status color function
function getOrderStatusColor(statusId?: number) {
  if (!statusId) return 'grey'

  // Unified status colors based on OrderStatus enum
  const statusColors: Record<number, string> = {
    1: 'yellow',        // ACTIVE
    2: 'blue',          // IN_PROGRESS
    3: 'positive',      // COMPLETED
    4: 'negative',      // NOT_ACTIVE
    5: 'red',           // CANCELLED
    6: 'green'          // DELIVERED
  }
  return statusColors[statusId] || 'grey'
}

// Get status name from order
function getOrderStatusName(order: IOrder) {
  return order?.orderStatus?.name || t('forAssignment')
}

// Add function to handle carrier change
const onCarrierChange = async (carrier: User | null) => {
  selectedDriver.value = null
  carrierDrivers.value = []

  if (carrier && carrier.id) {
    try {
      const drivers = await usersService.getAllUsersByCompanyIdAndRoleName(carrier.id.toString(), 'DRIVER')
      carrierDrivers.value = drivers || []
    } catch (error) {
      console.error('Error fetching carrier drivers:', error)
    }
  }
}

onMounted(async () => {
  if (props.order) {
    // Set the orderId for single order mode - ensure it's a number
    shipment.value.orderId = parseInt(props.order.id.toString())

    try {
      const osas = await OrderShippingAgentService.getOrderAgents(props.order.id.toString())
      if (osas) {
        executor.value = osas.find((osa: IOrderShippingAgent) => osa.isExecutor === true)
      }
    } catch (error) {
      console.error('Error fetching order agents:', error)
    }

    // Initialize addresses from order
    initializeAddresses()
  }
})

// Initialize addresses with order data when mounted
const initializeAddresses = () => {
  if (props.order) {
    // Initialize pickup address from order
    if (props.order.sourcePickupAddress) {
      shipment.value.pickupAddress = {
        id: '0',
        street: props.order.sourcePickupAddress.street || '',
        city: props.order.sourcePickupAddress.city || '',
        postalCode: props.order.sourcePickupAddress.postalCode || '',
        country: props.order.sourcePickupAddress.country || '',
        latitude: props.order.sourcePickupAddress.latitude || '',
        longitude: props.order.sourcePickupAddress.longitude || ''
      }
    }

    // Initialize destination address from order
    if (props.order.finalDestinationAddress) {
      shipment.value.destinationAddress = {
        id: '0',
        street: props.order.finalDestinationAddress.street || '',
        city: props.order.finalDestinationAddress.city || '',
        postalCode: props.order.finalDestinationAddress.postalCode || '',
        country: props.order.finalDestinationAddress.country || '',
        latitude: props.order.finalDestinationAddress.latitude || '',
        longitude: props.order.finalDestinationAddress.longitude || ''
      }
    }
  }
}

// Handle address selection from autocomplete
const getPlaceDetails = async (event: any, shipment: IShipment, addressType: 'pickup' | 'destination') => {
  const placeId = event.placeId
  const label = event.label.toLowerCase()

  try {
    const addr = await RouteService.fetchPlaceDetailsById(placeId)

    if (addressType === 'pickup' || label === t('from').toLowerCase()) {
      shipment.pickupAddress = {
        ...addr,
        id: '0'
      }
    }

    if (addressType === 'destination' || label === t('to').toLowerCase()) {
      shipment.destinationAddress = {
        ...addr,
        id: '0'
      }

      // Detect zone for destination address
      if (addr.latitude && addr.longitude) {
        await detectZoneForAddress(addr.latitude, addr.longitude)
      }
    }
  } catch (error) {
    console.error('Error fetching place details:', error)
  }
}

// Function to detect zone for coordinates
const detectZoneForAddress = async (latitude: string, longitude: string) => {
  isDetectingZone.value = true
  detectedZone.value = null

  try {
    const lat = parseFloat(latitude)
    const lng = parseFloat(longitude)

    if (!isNaN(lat) && !isNaN(lng)) {
      const zone = await zoneStore.findZoneByCoordinates(lat, lng)
      if (zone) {
        detectedZone.value = zone
        $q.notify({
          type: 'positive',
          message: t('zoneDetected', { zoneName: zone.name }),
          position: 'top'
        })
      }
    }
  } catch (error) {
    console.error('Error detecting zone:', error)
  } finally {
    isDetectingZone.value = false
  }
}

function futureDateTime (date: any) {
  const currentDate = new Date()
  const selectedDate = new Date(date)
  return selectedDate >= currentDate
}

// Fill source address
const fillSourceAddress = () => {
  if (!props.order?.sourcePickupAddress) return

  shipment.value.pickupAddress = {
    id: '0',
    street: props.order.sourcePickupAddress.street || '',
    city: props.order.sourcePickupAddress.city || '',
    postalCode: props.order.sourcePickupAddress.postalCode || '',
    country: props.order.sourcePickupAddress.country || '',
    latitude: props.order.sourcePickupAddress.latitude || '',
    longitude: props.order.sourcePickupAddress.longitude || ''
  }
}

// Fill destination address
const fillDestinationAddress = async () => {
  if (!props.order?.finalDestinationAddress) return

  shipment.value.destinationAddress = {
    id: '0',
    street: props.order.finalDestinationAddress.street || '',
    city: props.order.finalDestinationAddress.city || '',
    postalCode: props.order.finalDestinationAddress.postalCode || '',
    country: props.order.finalDestinationAddress.country || '',
    latitude: props.order.finalDestinationAddress.latitude || '',
    longitude: props.order.finalDestinationAddress.longitude || ''
  }

  // Detect zone for destination address
  if (props.order.finalDestinationAddress.latitude && props.order.finalDestinationAddress.longitude) {
    await detectZoneForAddress(
      props.order.finalDestinationAddress.latitude,
      props.order.finalDestinationAddress.longitude
    )
  }
}

// Submit the form
const submitForm = async () => {
  console.log('Submit form called') // Debug log

  // Validate form first
  if (formRef.value) {
    const isValid = await formRef.value.validate()
    console.log('Form validation result:', isValid) // Debug validation

    if (!isValid) {
      console.log('Form validation failed') // Debug log
      $q.notify({
        type: 'warning',
        message: t('pleaseFillAllRequiredFields'),
        position: 'top'
      })
      return
    }
  }

  isLoading.value = true

  try {
    // Determine which orders to process
    const ordersToAssign = props.multipleOrders && props.multipleOrders.length > 0
      ? props.multipleOrders
      : props.order ? [props.order] : []

    if (ordersToAssign.length === 0) {
      throw new Error('No orders selected')
    }

    // Check if assigning to shipping agent
    if (showShippingAgent.value && selectedShippingAgent.value) {
      console.log('Assigning to shipping agent...') // Debug log

      // Create OSA for each order
      const promises = ordersToAssign.map(async (order) => {
        const osa: IOrderShippingAgent = {
          id: 0,
          shippingAgentId: parseInt(selectedShippingAgent.value!.id.toString()),
          orderId: order.id,
          isExecutor: true,
          parentId: executor.value?.id ? parseInt(executor.value.id.toString()) : 0,
          childId: null
        }
        return OrderShippingAgentService.createOsa(osa)
      })

      await Promise.all(promises)
      emit('submit-success')
    } else if (selectedCarrier.value) {
      console.log('Assigning to carrier/driver...')

      // Create shipments for each order
      const shipments = ordersToAssign.map(order => {
        const processedShipment = { ...shipment.value }

        // Format dates
        if (processedShipment.pickupDate) {
          processedShipment.pickupDate = new Date(processedShipment.pickupDate)
        }

        if (processedShipment.deliveryDate) {
          processedShipment.deliveryDate = new Date(processedShipment.deliveryDate)
        }

        // Set carrier ID
        processedShipment.carrierId = parseInt(selectedCarrier.value!.id.toString())

        // Set driver ID if selected
        if (selectedDriver.value) {
          processedShipment.shipmentExecutorId = parseInt(selectedDriver.value.id.toString())
        }

        // Set the order ID for this specific order - ensure it's a number
        processedShipment.orderId = parseInt(order.id)

        // Copy addresses from the specific order if not already set
        if (order.sourcePickupAddress && !processedShipment.pickupAddress.street) {
          processedShipment.pickupAddress = {
            id: '0',
            street: order.sourcePickupAddress.street || '',
            city: order.sourcePickupAddress.city || '',
            postalCode: order.sourcePickupAddress.postalCode || '',
            country: order.sourcePickupAddress.country || '',
            latitude: order.sourcePickupAddress.latitude || '',
            longitude: order.sourcePickupAddress.longitude || ''
          }
        }

        if (order.finalDestinationAddress && !processedShipment.destinationAddress.street) {
          processedShipment.destinationAddress = {
            id: '0',
            street: order.finalDestinationAddress.street || '',
            city: order.finalDestinationAddress.city || '',
            postalCode: order.finalDestinationAddress.postalCode || '',
            country: order.finalDestinationAddress.country || '',
            latitude: order.finalDestinationAddress.latitude || '',
            longitude: order.finalDestinationAddress.longitude || ''
          }
        }

        // Clean the object for API submission
        const cleanShipment: any = {
          ...processedShipment,
          carrier: undefined,
          shipmentExecutor: undefined,
          shipmentStatus: undefined
        }

        delete cleanShipment.carrier
        delete cleanShipment.shipmentExecutor
        delete cleanShipment.shipmentStatus

        return cleanShipment
      })

      console.log('Shipments to create:', shipments)

      try {
        let createdShipmentIds: number[] = []

        if (shipments.length === 1) {
          const response = await ShipmentService.createShipment(shipments[0])
          if (response && response.data && response.data.id) {
            createdShipmentIds.push(parseInt(response.data.id))
          }
        } else {
          const response = await ShipmentService.createShipments(shipments)
          if (response && response.data) {
            createdShipmentIds = response.data.map((s: any) => parseInt(s.id))
          }
        }

        console.log('Shipments saved successfully')

        // Auto-assign zones to created shipments
        if (createdShipmentIds.length > 0 && detectedZone.value) {
          console.log('Auto-assigning zone to shipments:', detectedZone.value.name)

          for (const shipmentId of createdShipmentIds) {
            try {
              await zoneStore.autoAssignShipmentToZone(shipmentId)
            } catch (zoneError) {
              console.error(`Error auto-assigning zone to shipment ${shipmentId}:`, zoneError)
              // Don't fail the whole operation if zone assignment fails
            }
          }
        }

        emit('submit-success')
      } catch (error) {
        console.error('Error saving shipments:', error)
        throw error
      }
    } else {
      throw new Error('No carrier or shipping agent selected')
    }
  } catch (error) {
    console.error('Error submitting form:', error)
    $q.notify({
      type: 'negative',
      position: 'top',
      icon: 'error',
      message: showShippingAgent.value
        ? t('orderAssignedUnsuccesfully')
        : t('shipementAndCarrierAssignedUnsuccesfully')
    })
  } finally {
    isLoading.value = false
  }
}

watch([showShippingAgent, selectedShippingAgent, selectedCarrier, selectedDriver, shipment], () => {
  if (formRef.value) {
    formRef.value.validate().then((isValid: boolean) => {
      emit('validation-change', isValid)
    })
  }
}, { deep: true })

defineExpose({
  validateForm
})
</script>

<style lang="scss" scoped>
.col-6-on-desktop {
  @media (min-width: 1024px) {
    width: 50%;
  }
}

.order-details-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fafafa;

  .text-caption {
    font-weight: 500;
    opacity: 0.7;
  }

  .address-block {
    min-height: 50px;
    white-space: pre-line;
  }
}

.my-custom-toggle {
  .q-btn {
    min-width: 100px;
  }
}

/* Add max-width constraints for desktop screens */
@media (min-width: 1200px) {
  .q-card-section {
    max-width: 1200px;
    margin: 0 auto;
  }

  .q-form {
    max-width: 100%;
  }
}

/* Improve form field appearance */
.q-field {
  &.q-field--filled {
    .q-field__control {
      border-radius: 8px;

      &:hover {
        background: rgba(0, 0, 0, 0.03);
      }
    }
  }
}
</style>
