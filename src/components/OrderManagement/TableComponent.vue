<script setup lang="ts">
import { ref, watchEffect } from 'vue'
import { useRouter } from 'vue-router'
import IOrder from 'src/models/OrderModel'
import OrderService from 'src/services/OrderService'
import { formatDate, formatAddress } from 'src/models/FormatModels'
import { useQuasar } from 'quasar'
import { useI18n } from 'vue-i18n'
import { useOrderSelectionStore } from 'src/stores/order-selection-store'

const { t } = useI18n()
const orderSelectionStore = useOrderSelectionStore()

const $q = useQuasar()
const props = defineProps({
  tableData: {
    type: Array,
    required: true
  },
  tableColumns: {
    type: Array,
    required: true
  }
})

const router = useRouter()

const filtered = ref<IOrder[]>([])
const searchQuery = ref<string>('')
const tableData = ref<IOrder[]>(props.tableData as IOrder[])
const selectedOrders = ref<IOrder[]>([])
const confirm = ref(false)
filtered.value = tableData.value
console.log(tableData.value)

const emit = defineEmits(['order-deleted'])

async function deleteOrder (id: string) {
  try {
    $q.loading.show({ message: t('deletingOrder') })

    const deleted = await OrderService.deleteOrder(id)

    if (deleted) {
      tableData.value = tableData.value.filter(order => order.id !== id)

      $q.notify({
        type: 'positive',
        position: 'top',
        icon: 'check_circle',
        message: t('orderDeleted')
      })

      emit('order-deleted', id)
    } else {
      $q.notify({
        type: 'negative',
        position: 'top',
        icon: 'error',
        message: t('cantDeleteOrderWhileShipmenStatusActive')
      })
    }
  } catch (error) {
    console.error('Error deleting order:', error)
    $q.notify({
      type: 'negative',
      position: 'top',
      icon: 'error',
      message: t('errorDeletingOrder')
    })
  } finally {
    $q.loading.hide()
  }
}

async function editOrder (orderId: string) {
  router.push({
    name: 'add-order',
    query: { orderId }
  })
}

async function openOrderDetails (orderId: string) {
  router.push({
    name: 'order-details',
    query: { orderId }
  })
}

async function assignOrder (orderId: string) {
  router.push({
    name: 'assign-order',
    query: { orderId }
  })
}

// Updated function to forward multiple orders
const forwardOrders = () => {
  if (selectedOrders.value.length === 0) {
    $q.notify({
      type: 'warning',
      position: 'top',
      icon: 'warning',
      message: t('noOrdersSelected')
    })
    return
  }

  try {
    if (selectedOrders.value.length === 1) {
      // If just one order is selected, use the regular assignOrder function
      assignOrder(selectedOrders.value[0].id)
    } else {
      // Store the selected orders in Pinia store
      // Clone the orders to avoid reactivity issues
      const ordersToStore = JSON.parse(JSON.stringify(selectedOrders.value))
      orderSelectionStore.setSelectedOrders(ordersToStore)

      // Navigate to assign-order page
      router.push({
        name: 'assign-order',
        query: { batchAssignment: 'true' }
      })
    }
  } catch (error) {
    console.error('Error in forwardOrders:', error)
    $q.notify({
      type: 'negative',
      position: 'top',
      icon: 'error',
      message: t('errorForwardingOrders')
    })
  }

  // Close confirmation dialog if open
  confirm.value = false
}

watchEffect(() => {
  if (!searchQuery.value) {
    filtered.value = tableData.value
  } else {
    const query = searchQuery.value.toLowerCase()
    filtered.value = tableData.value.filter((item: IOrder) => {
      return (
        (item.trackingNumber != null && item.trackingNumber.toLowerCase().includes(query)) ||
        (item.id != null && item.id.toString().toLowerCase().includes(query)) ||
        (item.externalReference != null && item.externalReference.toLowerCase().includes(query)) ||
        (item.dsvIdentifier != null && item.dsvIdentifier.toLowerCase().includes(query))
        //(item.orderStatus != null && item.orderStatus.name.toLowerCase().includes(query))
      )
    })
  }
})

const csvFile = ref<File | null>(null)
const fileRef = ref(null)

const handleFileUpload = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target && target.files && target.files.length > 0) {
    csvFile.value = target.files[0]
    console.log(csvFile.value)
  }
}

const importDoc = async () => {
  console.log(csvFile.value)
  try {
    await OrderService.importOrder(csvFile.value)

    $q.notify({
      type: 'positive',
      position: 'top',
      icon: 'check_circle',
      message: t('succesfullyAddedOrders')
    })
  } catch (error) {
    $q.notify({
      type: 'negative',
      position: 'top',
      icon: 'error',
      message: t('errorAddingOrder')
    })
  }
}

const notifyAndClose = () => {
  $q.notify({
    type: 'positive',
    position: 'center',
    icon: 'check_circle',
    message: t('epodSuccesfullySend')// 'ePOD uspješno poslan!'
  })
}

const getSelectedString = (rows: IOrder[]) => {
  return selectedOrders.value.length === 0 ? '' : `${selectedOrders.value.length} record${selectedOrders.value.length > 1 ? 's' : ''} selected of ${filtered.value.length}`
}

function getStatusColor(statusId: number) {
  if (!statusId) return 'grey'

  // Unified status colors based on OrderStatus enum
  const statusColors: Record<number, string> = {
    1: 'yellow',        // CREATED
    2: 'blue',      // ASSIGNED
    3: 'positive',    // IN_PROGRESS
    4: 'green',        // COMPLETED
    5: 'negative',    // CANCELED
    6: 'red',         // FAILED

  }
  console.log("status color", statusColors[statusId]);
  console.log("status id", statusId);
  return statusColors[statusId] || 'grey'

}

function getStatusName(order: any) {
  return order?.orderStatus?.name || 'Unknown'
}

</script>

<template>
  <div class="q-pa-md spacing">
    <div style="margin-bottom: 50px;">
      <q-btn ripple icon="forward" color="primary" @click="confirm = true" style="float: left">
        {{ $t('forward') }}
        <q-badge v-if="selectedOrders.length > 0" color="white" text-color="primary" floating>
          {{ selectedOrders.length }}
        </q-badge>
      </q-btn>
      <q-btn to="/add-order" ripple icon="add" color="brand" style="float: right;">
        {{ $t('addNewOrder') }}
      </q-btn>
    </div>

    <div style="margin-bottom: 20px; width: 100%">
      <q-input
        color="grey"
        v-model="searchQuery"
        :label="$t('searchByOrderName')"
      >
        <template #append>
          <q-icon style="font-size: 1em" name="search" color="grey"/>
        </template>
      </q-input>
    </div>
    <div>
      <q-table
        :rows="filtered"
        :columns="props.tableColumns"
        row-key="id"
        :grid="$q.screen.xs"
        :sorting="true"
        :filtering="true"
        :rows-dense="true"
        selection="multiple"
        :selected-rows-label="getSelectedString"
        v-model:selected="selectedOrders"
        dense
      >
        <!--        <template #body-cell-orderStatus="props">-->
        <!--          <q-td key="orderStatus" :props="props">-->
        <!--            {{ props?.row?.orderStatus?.name }}-->
        <!--          </q-td>-->
        <!--        </template>-->

        <!--        <template #body-cell-orderType="props">-->
        <!--          <q-td key="orderType" :props="props">-->
        <!--            {{ props?.row?.orderType?.name }}-->
        <!--          </q-td>-->
        <!--        </template>-->
        <template #body-cell-trackingNumber="props">
          <q-td key="trackingNumber" :props="props">
            {{ props?.row?.trackingNumber }}
          </q-td>
        </template>
        <template #body-cell-sourcePickupAddress="props">
          <q-td key="sourcePickupAddress" :props="props">
            {{ formatAddress(props?.row?.sourcePickupAddress) }}
          </q-td>
        </template>
        <template #body-cell-dateCreated="props">
          <q-td key="dateCreated" :props="props">
            {{ formatDate(props?.row?.dateCreated) }}
          </q-td>
        </template>
        <template #body-cell-orderStatusId="props">
          <q-td key="orderStatusId" :props="props">
            <q-avatar size="sm" :color="getStatusColor(props.row.orderStatusId)" text-color="white">
              <q-tooltip>{{ getStatusName(props.row) }}</q-tooltip>
            </q-avatar>
          </q-td>
        </template>
        <template #body-cell-owner="props">
          <q-td key="owner" :props="props">
            <div v-if="props?.row?.owner != null">
              {{ props?.row?.owner.username }} - {{ props?.row?.owner.lastName }}, {{ props?.row?.owner.firstName }}
            </div>
            <div v-else>{{ $t('noOwner') }}</div>
          </q-td>
        </template>
        <template #body-cell-assign="props">
          <q-td key="assign">
            <div class="row float-right">
              <div class="col md-6">
                <q-btn
                  flat
                  round
                  color="brand"
                  icon="assignment_add"
                  size="md"
                  @click="assignOrder(props?.row?.id)"
                />
              </div>
            </div>
          </q-td>
        </template>
        <template #body-cell-editViewDelete="props">
          <q-td key="editViewDelete" :props="props">
            <div class="row float-right">
              <div class="col md-6">
                <q-btn
                  flat
                  round
                  color="grey-8"
                  icon="edit"
                  size="md"
                  @click="editOrder(props?.row?.id)"
                />
              </div>
              <div class="col md-6">
                <q-btn
                  flat
                  round
                  color="brand"
                  icon="visibility"
                  size="md"
                  @click="openOrderDetails(props?.row?.id)"
                />
              </div>
              <div class="col md-6">
                <q-btn
                  flat
                  round
                  color="red"
                  icon="delete"
                  size="md"
                  @click="deleteOrder(props?.row?.id)"
                />
              </div>
            </div>
          </q-td>
        </template>
        <template #body-cell-view="props">
          <q-td key="view" :props="props">
            <div class="row">
              <div class="col md-6">
                <q-btn
                  flat
                  round
                  color="primary"
                  icon="visibility"
                  size="md"
                  @click="openOrderDetails(props?.row?.id)"
                />
              </div>
            </div>
          </q-td>
        </template>
        <template #item="props">
          <div
            class="q-pa-xs col-xs-12 col-sm-6 col-md-4 col-lg-3 grid-style-transition "
          >
            <q-card :class="props.selected ? 'bg-grey-2 q-pa-lg ' : 'q-pa-lg'">
              <q-list dense>
                <div class="q-table__grid-item-row">
                  <div class="q-table__grid-item-title ">ID: {{ props?.row?.id }}</div>
                </div>
                <div class="q-table__grid-item-row">
                  <div class="q-table__grid-item-title">{{ $t('type') }}:</div>
                  <div class="q-table__grid-item-value flex items-center">
                    <span>{{ props?.row?.orderType?.name }}</span>
                    <q-icon v-if="props?.row?.orderTypeId && props?.row?.orderTypeId !== 1"
                            name="warning"
                            color="orange"
                            size="sm"
                            class="q-ml-xs"
                    >
                      <q-tooltip>{{ $t('dangerousGoods') }}</q-tooltip>
                    </q-icon>
                  </div>
                </div>
                <div class="q-table__grid-item-row">
                  <div class="q-table__grid-item-title">{{ $t('orderStatus') }}:</div>
                  <div class="q-table__grid-item-value">
                    <q-badge :color="getStatusColor(props.row.orderStatusId)" text-color="white">
                      {{ getStatusName(props.row) }}
                    </q-badge>
                  </div>
                </div>
                <div class="q-table__grid-item-row">
                  <div class="q-table__grid-item-title">{{ $t('trackingNumber') }}:</div>
                  <div class="q-table__grid-item-value">
                    {{ props?.row?.trackingNumber }} {{ props?.row?.name }}
                  </div>
                </div>
                <div class="q-table__grid-item-row">
                  <div class="q-table__grid-item-title">{{ $t('sourcePickupAddress') }}:</div>
                  <div class="q-table__grid-item-value">
                    {{ formatAddress(props?.row?.sourcePickupAddress) }}
                  </div>
                </div>
                <div class="q-table__grid-item-row">
                  <div class="q-table__grid-item-title">{{ $t('finalDestinationAddress') }}:</div>
                  <div class="q-table__grid-item-value">
                    {{ formatAddress(props?.row?.finalDestinationAddress) }}
                  </div>
                </div>
                <div class="q-table__grid-item-row">
                  <div class="q-table__grid-item-title">{{ $t('date') }}:</div>
                  <div class="q-table__grid-item-value">
                    {{ formatDate(props?.row?.dateCreated) }}
                  </div>
                </div>
                <div class="q-table__grid-item-row">
                  <div class="q-table__grid-item-title">{{ $t('carrier') }}:</div>
                  <div class="q-table__grid-item-value">
                    <template v-if="props?.row?.owner">
                      {{ props?.row?.owner.username }} - {{ props?.row?.owner.lastName }},
                      {{ props?.row?.owner.firstName }}
                    </template>
                    <template v-else>{{ $t('noOwner') }}</template>
                  </div>
                </div>
                <div>
                  <div class="q-table__grid-item-title">{{ $t('assignOrder') }}:</div>
                  <div class="q-table__grid-item-value">
                    <div class="row">
                      <div class="col md-6">
                        <q-btn
                          flat
                          round
                          color="red"
                          icon="assignment_add"
                          size="md"
                          @click="assignOrder(props?.row?.id)"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div class="q-table__grid-item-row">
                  <div class="q-table__grid-item-title">{{ $t('editDetailsDelete') }}:</div>
                  <div class="q-table__grid-item-value">
                    <div class="row">
                      <div class="col md-6">
                        <q-btn
                          flat
                          round
                          color="primary"
                          icon="edit"
                          size="md"
                          @click="editOrder(props?.row?.id)"
                        />
                      </div>
                      <div class="col md-6">
                        <q-btn
                          flat
                          round
                          color="primary"
                          icon="visibility"
                          size="md"
                          @click="openOrderDetails(props?.row?.id)"
                        />
                      </div>
                      <div class="col md-6">
                        <q-btn
                          flat
                          round
                          color="red"
                          icon="delete"
                          size="md"
                          @click="deleteOrder(props?.row?.id)"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </q-list>
            </q-card>
          </div>
        </template>
      </q-table>
    </div>
    <q-dialog v-model="confirm" persistent>
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="priority_high" color="warning" text-color="white" />
          <span class="q-ml-sm">
            {{ $t('confirmForwardSelectedOrders', { count: selectedOrders.length }) }}
          </span>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="$t('cancel')" color="amber" v-close-popup />
          <q-btn flat :label="$t('confirm')" color="secondary" @click="forwardOrders" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>
<style scoped>

.label-above .q-field__label {
  transform: translateY(-30px);
  font-size: 12px;
}

.q-table__grid-item-value .col {
  margin-right: 10px !important;
}

.q-table__grid-item-value .col {
  flex: 1 !important;
}

.q-table__grid-item-value .q-btn {
  font-size: 14px !important;
}
</style>
